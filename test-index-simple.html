<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <title>Test Index Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #fbbf24;
        }
        
        .status {
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
            text-align: center;
        }
        
        .success { background: rgba(34, 197, 94, 0.8); }
        .warning { background: rgba(251, 191, 36, 0.8); }
        .error { background: rgba(239, 68, 68, 0.8); }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .info-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .test-button {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
            transition: transform 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .result-text {
            font-size: 18px;
            margin: 15px 0;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Index.html</h1>
        
        <div class="test-section">
            <h3>📱 Device Information</h3>
            <div class="info-grid">
                <div class="info-card">
                    <strong>Screen Size</strong><br>
                    <span id="screen-size">Loading...</span>
                </div>
                <div class="info-card">
                    <strong>Window Size</strong><br>
                    <span id="window-size">Loading...</span>
                </div>
                <div class="info-card">
                    <strong>Device Type</strong><br>
                    <span id="device-type">Loading...</span>
                </div>
                <div class="info-card">
                    <strong>Expected Layout</strong><br>
                    <span id="expected-layout">Loading...</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎯 Responsive Test</h3>
            <div id="responsive-status" class="status warning">
                Analyzing...
            </div>
            <div class="result-text" id="result-text">
                Checking responsive behavior...
            </div>
        </div>
        
        <div class="test-section">
            <h3>🚀 Test Actions</h3>
            <div style="text-align: center;">
                <a href="Index.html" class="test-button">
                    📄 Open Index.html
                </a>
                <button onclick="location.reload()" class="test-button">
                    🔄 Refresh Test
                </button>
                <a href="debug-mobile.html" class="test-button">
                    🔍 Debug Tool
                </a>
            </div>
        </div>
        
        <div class="test-section">
            <h3>✅ What to Check in Index.html</h3>
            <ul style="text-align: left; line-height: 1.8;">
                <li><strong>Header:</strong> Logo dan menu harus vertikal (tidak horizontal)</li>
                <li><strong>Program Cards:</strong> 3 cards harus menjadi 1 kolom</li>
                <li><strong>News Cards:</strong> Berita harus menjadi 1 kolom</li>
                <li><strong>CTA Buttons:</strong> 2 buttons harus vertikal</li>
                <li><strong>No Horizontal Scroll:</strong> Tidak ada scroll ke kanan</li>
                <li><strong>Text Size:</strong> Font harus readable di mobile</li>
            </ul>
        </div>
    </div>

    <script>
        function updateInfo() {
            // Device info
            document.getElementById('screen-size').textContent = `${window.screen.width} x ${window.screen.height}`;
            document.getElementById('window-size').textContent = `${window.innerWidth} x ${window.innerHeight}`;
            
            // Device type detection
            const isMobileWidth = window.innerWidth <= 768;
            const isMobileScreen = window.screen.width <= 768;
            const isMobileUA = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            let deviceType = 'Desktop';
            if (isMobileUA && isMobileScreen) deviceType = 'Mobile Device';
            else if (isMobileWidth) deviceType = 'Mobile View';
            else if (isMobileUA) deviceType = 'Mobile Browser';
            
            document.getElementById('device-type').textContent = deviceType;
            
            // Expected layout
            const shouldBeMobile = isMobileWidth || isMobileScreen || isMobileUA;
            document.getElementById('expected-layout').textContent = shouldBeMobile ? 'Mobile Layout' : 'Desktop Layout';
            
            // Responsive status
            const statusEl = document.getElementById('responsive-status');
            const resultEl = document.getElementById('result-text');
            
            if (shouldBeMobile) {
                statusEl.className = 'status success';
                statusEl.textContent = '✅ Should be Mobile Layout';
                resultEl.innerHTML = `
                    <strong>Index.html should display:</strong><br>
                    • Single column layout<br>
                    • Vertical navigation<br>
                    • Stacked cards<br>
                    • Mobile-friendly text sizes
                `;
            } else {
                statusEl.className = 'status warning';
                statusEl.textContent = '🖥️ Should be Desktop Layout';
                resultEl.innerHTML = `
                    <strong>Index.html should display:</strong><br>
                    • Multi-column layout<br>
                    • Horizontal navigation<br>
                    • Side-by-side cards<br>
                    • Desktop text sizes
                `;
            }
        }
        
        // Update on load and resize
        updateInfo();
        window.addEventListener('resize', updateInfo);
        window.addEventListener('orientationchange', function() {
            setTimeout(updateInfo, 100);
        });
        
        // Auto-update every 3 seconds
        setInterval(updateInfo, 3000);
    </script>
</body>
</html>

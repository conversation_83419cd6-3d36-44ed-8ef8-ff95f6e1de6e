<!DOCTYPE html>
<html lang="id">
<head>
    <base target="_top">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Ajukan Kemitraan - Relawan TIK Kaltim</title>
    <meta name="description" content="Ajukan kemitraan dengan Relawan TIK Kalimantan Timur - Formulir Pengajuan">
    <?!= include('Stylesheet'); ?>
</head>
<body>
    <!-- HEADER -->
    <header class="site-header">
      <div class="header-container">
        <div class="logo">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Relawan TIK Kaltim</a>
        </div>
        <nav class="nav-links">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a>
        </nav>
        <div class="header-cta">
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=gabung_relawan" class="cta-button">Gabung Relawan!</a>
        </div>
      </div>
    </header>

    <main>
        <section class="page-header">
            <h1>Ajukan Kemitraan</h1>
            <p>Mari berkolaborasi untuk memberikan dampak yang lebih luas.</p>
        </section>
        <section class="form-section">
            <div class="form-container">
                <h2>Formulir Pengajuan Kemitraan</h2>
                <p>Silakan isi detail institusi dan proposal singkat Anda. Tim kami akan menindaklanjutinya.</p>
                <form id="partnership-form">
                    <div class="form-group">
                        <label for="nama_instansi">Nama Institusi / Organisasi</label>
                        <input type="text" id="nama_instansi" name="nama_instansi" required>
                    </div>
                    <div class="form-group">
                        <label for="nama_pic">Nama Narahubung (PIC)</label>
                        <input type="text" id="nama_pic" name="nama_pic" required>
                    </div>
                    <div class="form-group">
                        <label for="email_pic">Email Narahubung</label>
                        <input type="email" id="email_pic" name="email_pic" required>
                    </div>
                    <div class="form-group">
                        <label for="wa_pic">Nomor WhatsApp Narahubung</label>
                        <input type="tel" id="wa_pic" name="wa_pic" required>
                    </div>
                    <div class="form-group">
                        <label for="deskripsi_kemitraan">Deskripsi Singkat Program / Kemitraan</label>
                        <textarea id="deskripsi_kemitraan" name="deskripsi_kemitraan" rows="6" required></textarea>
                    </div>
                    <div class="form-group">
                        <button type="submit" id="submit-button">Kirim Pengajuan</button>
                    </div>
                </form>
                <div id="status-message"></div>
            </div>
        </section>
    </main>

    <!-- FOOTER -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-about"><h3>Relawan TIK Kaltim</h3><p>Organisasi sosial nirlaba yang mendedikasikan diri untuk akselerasi dan penetrasi literasi digital di Kalimantan Timur.</p></div>
            <div class="footer-links"><h3>Navigasi</h3><ul><li><a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a></li></ul></div>
            <div class="footer-contact"><h3>Hubungi Kami</h3><p>Email: <EMAIL></p><p>Sekretariat: [Alamat Sekretariat]</p></div>
            <div class="footer-social"><h3>Ikuti Kami</h3><div class="social-icons"><a href="#">[FB]</a> <a href="#">[IG]</a> <a href="#">[TW]</a> <a href="#">[YT]</a></div></div>
        </div>
        <div class="footer-bottom"><p>© 2025 Relawan TIK Provinsi Kalimantan Timur. Hak Cipta Dilindungi.</p></div>
    </footer>

    <script>
      const form = document.getElementById('partnership-form');
      const submitButton = document.getElementById('submit-button');
      const statusMessage = document.getElementById('status-message');

      form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        submitButton.disabled = true;
        submitButton.textContent = 'Mengirim...';
        statusMessage.textContent = '';
        statusMessage.className = '';

        const formData = {
          nama_instansi: form.nama_instansi.value,
          nama_pic: form.nama_pic.value,
          email_pic: form.email_pic.value,
          wa_pic: form.wa_pic.value,
          deskripsi_kemitraan: form.deskripsi_kemitraan.value
        };

        google.script.run
          .withSuccessHandler(function(response) {
            if (response.status === "success") {
              statusMessage.textContent = response.message;
              statusMessage.classList.add('success');
              form.reset();
            } else {
              statusMessage.textContent = 'Gagal: ' + response.message;
              statusMessage.classList.add('error');
            }
            submitButton.disabled = false;
            submitButton.textContent = 'Kirim Pengajuan';
          })
          .withFailureHandler(function(error) {
            statusMessage.textContent = 'Gagal: Terjadi kesalahan: ' + error.message;
            statusMessage.classList.add('error');
            submitButton.disabled = false;
            submitButton.textContent = 'Kirim Pengajuan';
          })
          .savePartnershipData(formData); // Panggil fungsi baru
      });

      // Script untuk memastikan responsive bekerja di Google Apps Script
      (function() {
        // Memastikan viewport meta tag bekerja
        function setViewport() {
          var viewport = document.querySelector('meta[name="viewport"]');
          if (viewport) {
            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
          }
        }

        // Mencegah zoom pada input focus di iOS
        function preventZoom() {
          var inputs = document.querySelectorAll('input, textarea, select');
          inputs.forEach(function(input) {
            input.addEventListener('focus', function() {
              if (window.innerWidth < 768) {
                var fontSize = window.getComputedStyle(input).fontSize;
                if (parseInt(fontSize) < 16) {
                  input.style.fontSize = '16px';
                }
              }
            });
          });
        }

        // Memastikan tidak ada horizontal scroll
        function preventHorizontalScroll() {
          document.body.style.overflowX = 'hidden';
          document.documentElement.style.overflowX = 'hidden';
        }

        // Jalankan saat DOM ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', function() {
            setViewport();
            preventZoom();
            preventHorizontalScroll();
          });
        } else {
          setViewport();
          preventZoom();
          preventHorizontalScroll();
        }

        // Jalankan saat window resize
        window.addEventListener('resize', function() {
          preventHorizontalScroll();
        });
      })();
    </script>

</body>
</html>

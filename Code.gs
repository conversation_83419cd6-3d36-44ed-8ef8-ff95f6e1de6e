/**
 * <PERSON><PERSON><PERSON> utama yang dijalankan saat URL aplikasi web diakses.
 * Berfungsi sebagai router untuk menampilkan halaman yang berbeda.
 * @param {Object} e - Parameter event dari request GET.
 * @returns {HtmlOutput} - Halaman HTML yang akan ditampilkan.
 */
function doGet(e) {
  const page = e.parameter.page || 'beranda'; // Default ke halaman beranda
  let template;
  let data = {};

  switch (page) {
    case 'beranda':
      template = HtmlService.createTemplateFromFile('Index');
      const allArticles = getNewsArticles(); // Ambil semua berita
      data.latestNews = allArticles.slice(0, 3); // Ambil 3 berita teratas
      template.latestNews = data.latestNews; // Kirim 3 berita itu ke template
      break;
    case 'tentang_kami':
      template = HtmlService.createTemplateFromFile('TentangKami');
      break;
    case 'program':
      template = HtmlService.createTemplateFromFile('Program');
      break;
    case 'berita':
      template = HtmlService.createTemplateFromFile('Berita');
      data.newsArticles = getNewsArticles(); // Ambil data berita
      template.newsArticles = data.newsArticles;
      break;
    case 'artikel':
      const articleId = e.parameter.id;
      template = HtmlService.createTemplateFromFile('Artikel');
      data.article = getArticleById(articleId); // Ambil satu artikel berdasarkan ID
      template.article = data.article;
      break;
    case 'gabung_relawan':
      template = HtmlService.createTemplateFromFile('GabungRelawan');
      break;
    case 'kemitraan': 
      template = HtmlService.createTemplateFromFile('Kemitraan');
      break;
    default:
      template = HtmlService.createTemplateFromFile('Index');
      const defaultArticles = getNewsArticles();
      data.latestNews = defaultArticles.slice(0, 3);
      template.latestNews = data.latestNews;
  }
  
  return template.evaluate().setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * Menyertakan konten file HTML lain ke dalam template.
 * @param {string} filename - Nama file HTML yang akan disertakan.
 * @returns {string} - Konten dari file HTML.
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

// ==========================================================
// FUNGSI UNTUK MENGAMBIL DATA DARI GOOGLE SHEET
// ==========================================================
const SPREADSHEET_ID = "1zJrWv3kYQquWtrPi4avlquUykIPdv625ocnETBhpFuk";
const SPREADSHEET = SpreadsheetApp.openById(SPREADSHEET_ID);
const NEWS_SHEET = SPREADSHEET.getSheetByName("Berita");
const VOLUNTEER_SHEET = SPREADSHEET.getSheetByName("Pendaftar");
const PARTNERSHIP_SHEET = SPREADSHEET.getSheetByName("Kemitraan");

/**
 * Mengambil semua data artikel berita dari Google Sheet.
 * @returns {Array<Object>} - Array objek yang berisi data berita.
 */
function getNewsArticles() {
  const data = NEWS_SHEET.getDataRange().getValues();
  const headers = data.shift(); // Ambil baris pertama sebagai header
  
  // Periksa apakah sheet kosong atau hanya berisi header
  if (data.length === 0) {
    return [];
  }
  
  const articles = data.map(row => {
    let article = {};
    headers.forEach((header, index) => {
      article[header] = row[index];
    });
    return article;
  });
  
  return articles.reverse(); // Tampilkan yang terbaru dulu
}

/**
 * Mengambil satu artikel berita berdasarkan ID.
 * @param {string} id - ID unik dari artikel yang dicari.
 * @returns {Object|null} - Objek artikel jika ditemukan, atau null jika tidak.
 */
function getArticleById(id) {
  const data = NEWS_SHEET.getDataRange().getValues();
  const headers = data.shift();
  
  if (data.length === 0) {
    return null;
  }
  
  const idColumnIndex = headers.indexOf('id');
  
  for (let i = 0; i < data.length; i++) {
    if (data[i][idColumnIndex].toString() === id) {
      let article = {};
      headers.forEach((header, index) => {
        article[header] = data[i][index];
      });
      return article;
    }
  }
  return null; // Tidak ditemukan
}

// ==========================================================
// FUNGSI UNTUK MENYIMPAN DATA DARI FORMULIR
// ==========================================================

/**
 * Menyimpan data dari formulir pendaftaran relawan ke Google Sheet.
 * @param {Object} formData - Data dari formulir.
 * @returns {Object} - Status dan pesan hasil operasi.
 */
function saveVolunteerData(formData) {
  try {
    const timestamp = new Date();
    VOLUNTEER_SHEET.appendRow([
      timestamp,
      formData.nama,
      formData.email,
      formData.no_wa,
      formData.domisili,
      formData.alasan
    ]);
    return { status: "success", message: "Pendaftaran berhasil terkirim! Terima kasih." };
  } catch (error) {
    return { status: "error", message: "Terjadi kesalahan: " + error.message };
  }
}

/**
 * Menyimpan data dari formulir pengajuan kemitraan.
 * @param {Object} formData - Data dari formulir.
 * @returns {Object} - Status dan pesan hasil operasi.
 */
function savePartnershipData(formData) {
  try {
    const timestamp = new Date();
    PARTNERSHIP_SHEET.appendRow([
      timestamp,
      formData.nama_instansi,
      formData.nama_pic,
      formData.email_pic,
      formData.wa_pic,
      formData.deskripsi_kemitraan
    ]);
    return { status: "success", message: "Proposal kemitraan berhasil terkirim! Kami akan segera menghubungi Anda." };
  } catch (error) {
    return { status: "error", message: "Terjadi kesalahan: " + error.message };
  }
}


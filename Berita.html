<!DOCTYPE html>
<html lang="id">
  <head>
    <base target="_top">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Berita & Artikel - Relawan TIK Kaltim</title>
    <meta name="description" content="Berita dan artikel terbaru dari Relawan TIK Kalimantan Timur">
    <?!= include('Stylesheet'); ?>
  </head>
  <body>
    <!-- HEADER -->
    <header class="site-header">
      <div class="header-container">
        <div class="logo">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Relawan TIK Kaltim</a>
        </div>
        <nav class="nav-links">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a>
        </nav>
        <div class="header-cta">
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=gabung_relawan" class="cta-button">Gabung Relawan!</a>
        </div>
      </div>
    </header>

    <main>
        <section class="page-header">
            <h1>Berita & Artikel</h1>
            <p>Ikuti informasi dan kegiatan terbaru dari kami.</p>
        </section>
        <section class="news-list-section">
            <div class="news-list-container">
                <? for (var i = 0; i < newsArticles.length; i++) { ?>
                  <? var article = newsArticles[i]; ?>
                  <article class="news-card">
                      <img src="<?= article.gambar_url ?>" alt="Gambar untuk artikel: <?= article.judul ?>" class="news-image">
                      <div class="news-content">
                          <p class="news-meta">Kategori: <?= article.kategori ?></p>
                          <h3 class="news-title"><?= article.judul ?></h3>
                          <p class="news-excerpt"><?= article.kutipan ?></p>
                          <a href="<?= ScriptApp.getService().getUrl() ?>?page=artikel&id=<?= article.id ?>" class="read-more-link">Baca Selengkapnya →</a>
                      </div>
                  </article>
                <? } ?>
                <? if (newsArticles.length === 0) { ?>
                  <p>Saat ini belum ada berita yang tersedia.</p>
                <? } ?>
            </div>
        </section>
    </main>
    
    <!-- FOOTER -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-about"><h3>Relawan TIK Kaltim</h3><p>Organisasi sosial nirlaba yang mendedikasikan diri untuk akselerasi dan penetrasi literasi digital di Kalimantan Timur.</p></div>
            <div class="footer-links"><h3>Navigasi</h3><ul><li><a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a></li></ul></div>
            <div class="footer-contact"><h3>Hubungi Kami</h3><p>Email: <EMAIL></p><p>Sekretariat: [Alamat Sekretariat]</p></div>
            <div class="footer-social"><h3>Ikuti Kami</h3><div class="social-icons"><a href="#">[FB]</a> <a href="#">[IG]</a> <a href="#">[TW]</a> <a href="#">[YT]</a></div></div>
        </div>
        <div class="footer-bottom"><p>© 2025 Relawan TIK Provinsi Kalimantan Timur. Hak Cipta Dilindungi.</p></div>
    </footer>

    <script>
      // Script untuk memastikan responsive bekerja di Google Apps Script
      (function() {
        // Memastikan viewport meta tag bekerja
        function setViewport() {
          var viewport = document.querySelector('meta[name="viewport"]');
          if (viewport) {
            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
          }
        }

        // Memastikan tidak ada horizontal scroll
        function preventHorizontalScroll() {
          document.body.style.overflowX = 'hidden';
          document.documentElement.style.overflowX = 'hidden';
        }

        // Jalankan saat DOM ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', function() {
            setViewport();
            preventHorizontalScroll();
          });
        } else {
          setViewport();
          preventHorizontalScroll();
        }

        // Jalankan saat window resize
        window.addEventListener('resize', function() {
          preventHorizontalScroll();
        });
      })();
    </script>

  </body>
</html>


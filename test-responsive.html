<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title>Test Responsive - Relawan TIK Kaltim</title>
    
    <style>
        /* Force responsive behavior */
        html {
            width: 100vw !important;
            max-width: 100vw !important;
            overflow-x: hidden !important;
            -webkit-text-size-adjust: 100% !important;
            -ms-text-size-adjust: 100% !important;
            box-sizing: border-box !important;
        }
        
        body {
            width: 100vw !important;
            max-width: 100vw !important;
            overflow-x: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
            font-family: Arial, sans-serif;
            background: linear-gradient(45deg, #1e3a8a, #2563eb);
            color: white;
            min-height: 100vh;
        }
        
        * {
            box-sizing: border-box !important;
        }
        
        .container {
            padding: 20px;
            text-align: center;
        }
        
        .test-box {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .device-info {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-family: monospace;
        }
        
        .responsive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .grid-item {
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 8px;
            min-height: 100px;
        }
        
        @media screen and (max-width: 768px) {
            .container {
                padding: 15px !important;
            }
            
            .test-box {
                padding: 15px !important;
                margin: 15px 0 !important;
            }
            
            .responsive-grid {
                grid-template-columns: 1fr !important;
                gap: 10px !important;
            }
            
            h1 {
                font-size: 24px !important;
            }
            
            h2 {
                font-size: 20px !important;
            }
        }
        
        @media screen and (max-width: 480px) {
            .container {
                padding: 10px !important;
            }
            
            h1 {
                font-size: 20px !important;
            }
            
            h2 {
                font-size: 18px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Responsivitas Website</h1>
        <p>File ini untuk mengecek apakah responsivitas bekerja dengan baik</p>
        
        <div class="test-box">
            <h2>📱 Informasi Device</h2>
            <div class="device-info" id="device-info">
                Loading device info...
            </div>
        </div>
        
        <div class="test-box">
            <h2>📐 Test Grid Responsive</h2>
            <div class="responsive-grid">
                <div class="grid-item">Grid Item 1</div>
                <div class="grid-item">Grid Item 2</div>
                <div class="grid-item">Grid Item 3</div>
                <div class="grid-item">Grid Item 4</div>
            </div>
        </div>
        
        <div class="test-box">
            <h2>✅ Checklist Responsivitas</h2>
            <div id="responsive-checks">
                Loading checks...
            </div>
        </div>
        
        <div class="test-box">
            <h2>🔗 Link ke Website Utama</h2>
            <p>Jika test ini responsive, maka website utama juga seharusnya responsive</p>
            <a href="Index.html" style="color: #fbbf24; text-decoration: underline;">
                Kembali ke Website Utama
            </a>
        </div>
    </div>

    <script>
        // Script untuk menampilkan informasi device dan test responsivitas
        (function() {
            function updateDeviceInfo() {
                const deviceInfo = document.getElementById('device-info');
                const checks = document.getElementById('responsive-checks');
                
                const info = {
                    'Screen Width': window.screen.width + 'px',
                    'Screen Height': window.screen.height + 'px',
                    'Window Width': window.innerWidth + 'px',
                    'Window Height': window.innerHeight + 'px',
                    'Device Pixel Ratio': window.devicePixelRatio,
                    'User Agent': navigator.userAgent.substring(0, 50) + '...',
                    'Touch Support': 'ontouchstart' in window ? 'Yes' : 'No',
                    'Orientation': window.orientation !== undefined ? window.orientation + '°' : 'Unknown'
                };
                
                let infoHTML = '';
                for (const [key, value] of Object.entries(info)) {
                    infoHTML += `<div><strong>${key}:</strong> ${value}</div>`;
                }
                deviceInfo.innerHTML = infoHTML;
                
                // Responsive checks
                const isMobile = window.innerWidth <= 768;
                const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
                const isDesktop = window.innerWidth > 1024;
                
                const checksHTML = `
                    <div style="text-align: left;">
                        <div>📱 Mobile (≤768px): ${isMobile ? '✅ Active' : '❌ Inactive'}</div>
                        <div>📟 Tablet (769-1024px): ${isTablet ? '✅ Active' : '❌ Inactive'}</div>
                        <div>🖥️ Desktop (>1024px): ${isDesktop ? '✅ Active' : '❌ Inactive'}</div>
                        <div>🚫 Horizontal Scroll: ${document.body.scrollWidth > window.innerWidth ? '❌ Present' : '✅ None'}</div>
                        <div>📏 Viewport Meta: ${document.querySelector('meta[name="viewport"]') ? '✅ Present' : '❌ Missing'}</div>
                    </div>
                `;
                checks.innerHTML = checksHTML;
            }
            
            // Update info saat load dan resize
            updateDeviceInfo();
            window.addEventListener('resize', updateDeviceInfo);
            window.addEventListener('orientationchange', function() {
                setTimeout(updateDeviceInfo, 100);
            });
        })();
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="id">
  <head>
    <base target="_top">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Tentang Kami - Relawan TIK Kaltim</title>
    <meta name="description" content="Mengenal lebih dekat Relawan TIK Kalimantan Timur - Sejarah, <PERSON><PERSON>, dan <PERSON>">
    <?!= include('Stylesheet'); ?>
  </head>
  <body>
    <!-- HEADER -->
    <header class="site-header">
      <div class="header-container">
        <div class="logo">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Relawan TIK Kaltim</a>
        </div>
        <nav class="nav-links">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a>
        </nav>
        <div class="header-cta">
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=gabung_relawan" class="cta-button">Gabung Relawan!</a>
        </div>
      </div>
    </header>

    <main>
        <section class="page-header">
            <h1>Tentang Kami</h1>
            <p>Mengenal lebih dekat Relawan TIK Kalimantan Timur.</p>
        </section>
        <section class="about-section">
            <div class="about-container">
                <article class="about-content">
                    <h2>Sejarah Singkat</h2>
                    <p>Relawan TIK Kalimantan Timur merupakan bagian dari Gerakan Nasional Relawan TIK Indonesia yang dideklarasikan pada tahun 2011. Berawal dari keprihatinan atas kesenjangan digital, sekelompok pegiat TIK di Kaltim berinisiatif untuk membentuk sebuah komunitas yang bertujuan untuk membantu masyarakat agar lebih melek teknologi. Sejak saat itu, kami terus bergerak, berkolaborasi dengan pemerintah, institusi pendidikan, dan komunitas lokal untuk menyebarkan semangat literasi digital ke seluruh penjuru Benua Etam.</p>
                </article>
                <article class="about-content">
                    <h2>Visi & Misi</h2>
                    <p><b>Visi:</b> Menjadi motor penggerak terwujudnya masyarakat Kalimantan Timur yang cerdas, kreatif, dan produktif berbasis Teknologi Informasi dan Komunikasi.</p>
                    <p><b>Misi:</b></p>
                    <ul>
                        <li>Meningkatkan literasi digital masyarakat melalui edukasi dan sosialisasi.</li>
                        <li>Melakukan pendampingan pemanfaatan TIK untuk sektor UMKM, pendidikan, dan pemerintahan.</li>
                        <li>Mengembangkan kapasitas dan kompetensi para relawan TIK di Kalimantan Timur.</li>
                        <li>Menjalin kemitraan strategis dengan berbagai pihak untuk akselerasi program.</li>
                    </ul>
                </article>
                <article class="about-content">
                    <h2>Struktur Organisasi</h2>
                    <p>Berikut adalah jajaran pengurus inti Relawan TIK Kaltim periode saat ini.</p>
                    <div class="profile-card-container">
                        <div class="profile-card"><img src="https://placehold.co/150x150/dbeafe/1e3a8a?text=Foto" alt="Foto Ketua"><h4>Nama Ketua</h4><p>Ketua Umum</p></div>
                        <div class="profile-card"><img src="https://placehold.co/150x150/dbeafe/1e3a8a?text=Foto" alt="Foto Sekretaris"><h4>Nama Sekretaris</h4><p>Sekretaris Umum</p></div>
                        <div class="profile-card"><img src="https://placehold.co/150x150/dbeafe/1e3a8a?text=Foto" alt="Foto Bendahara"><h4>Nama Bendahara</h4><p>Bendahara Umum</p></div>
                        <div class="profile-card"><img src="https://placehold.co/150x150/dbeafe/1e3a8a?text=Foto" alt="Foto Kadiv"><h4>Nama Kadiv</h4><p>Ketua Divisi</p></div>
                    </div>
                </article>
            </div>
        </section>
    </main>

    <!-- FOOTER -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-about"><h3>Relawan TIK Kaltim</h3><p>Organisasi sosial nirlaba yang mendedikasikan diri untuk akselerasi dan penetrasi literasi digital di Kalimantan Timur.</p></div>
            <div class="footer-links"><h3>Navigasi</h3><ul><li><a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a></li></ul></div>
            <div class="footer-contact"><h3>Hubungi Kami</h3><p>Email: <EMAIL></p><p>Sekretariat: [Alamat Sekretariat]</p></div>
            <div class="footer-social"><h3>Ikuti Kami</h3><div class="social-icons"><a href="#">[FB]</a> <a href="#">[IG]</a> <a href="#">[TW]</a> <a href="#">[YT]</a></div></div>
        </div>
        <div class="footer-bottom"><p>© 2025 Relawan TIK Provinsi Kalimantan Timur. Hak Cipta Dilindungi.</p></div>
    </footer>

    <script>
      // Script untuk memastikan responsive bekerja di Google Apps Script
      (function() {
        // Memastikan viewport meta tag bekerja
        function setViewport() {
          var viewport = document.querySelector('meta[name="viewport"]');
          if (viewport) {
            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
          }
        }

        // Memastikan tidak ada horizontal scroll
        function preventHorizontalScroll() {
          document.body.style.overflowX = 'hidden';
          document.documentElement.style.overflowX = 'hidden';
        }

        // Jalankan saat DOM ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', function() {
            setViewport();
            preventHorizontalScroll();
          });
        } else {
          setViewport();
          preventHorizontalScroll();
        }

        // Jalankan saat window resize
        window.addEventListener('resize', function() {
          preventHorizontalScroll();
        });
      })();
    </script>

  </body>
</html>


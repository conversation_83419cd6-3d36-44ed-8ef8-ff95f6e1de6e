<!DOCTYPE html>
<html lang="id">
  <head>
    <base target="_top">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title>Relawan TIK Kaltim - Beranda</title>
    <meta name="description" content="Relawan TIK Kalimantan Timur - Mengawal Literasi Digital di Benua Etam">

    <!-- CSS SUPER AGRESIF untuk memaksa responsivitas -->
    <style>
      /* FORCE RESPONSIVE - PRIORITAS TERTINGGI */
      html {
        width: 100vw !important;
        max-width: 100vw !important;
        overflow-x: hidden !important;
        -webkit-text-size-adjust: 100% !important;
        -ms-text-size-adjust: 100% !important;
        box-sizing: border-box !important;
      }

      body {
        width: 100vw !important;
        max-width: 100vw !important;
        overflow-x: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        box-sizing: border-box !important;
        font-family: 'Inter', sans-serif !important;
        background-color: #f9fafb !important;
      }

      * {
        box-sizing: border-box !important;
        max-width: 100% !important;
      }

      /* MOBILE DETECTION - FORCE MOBILE LAYOUT */
      @media screen and (max-width: 768px),
             screen and (max-device-width: 768px),
             only screen and (max-width: 768px) {

        html, body {
          width: 100vw !important;
          max-width: 100vw !important;
          overflow-x: hidden !important;
          font-size: 14px !important;
        }

        /* HEADER MOBILE */
        .site-header {
          width: 100% !important;
          padding: 0 10px !important;
          background-color: #ffffff !important;
          border-bottom: 1px solid #e5e7eb !important;
        }

        .header-container {
          display: flex !important;
          flex-direction: column !important;
          width: 100% !important;
          text-align: center !important;
          padding: 10px 0 !important;
          height: auto !important;
        }

        .logo a {
          font-size: 18px !important;
          font-weight: 700 !important;
          color: #1e3a8a !important;
          text-decoration: none !important;
        }

        .nav-links {
          display: flex !important;
          flex-wrap: wrap !important;
          justify-content: center !important;
          gap: 8px !important;
          margin: 10px 0 !important;
          width: 100% !important;
        }

        .nav-links a {
          font-size: 12px !important;
          padding: 6px 10px !important;
          color: #4b5563 !important;
          text-decoration: none !important;
          border-radius: 4px !important;
          background-color: #f3f4f6 !important;
        }

        .header-cta {
          margin-top: 10px !important;
        }

        .cta-button {
          background-color: #2563eb !important;
          color: #ffffff !important;
          padding: 8px 16px !important;
          border-radius: 6px !important;
          text-decoration: none !important;
          font-size: 12px !important;
          font-weight: 500 !important;
        }

        /* HERO SECTION MOBILE */
        .hero-section {
          background-color: #e0e7ff !important;
          text-align: center !important;
          padding: 30px 15px !important;
          width: 100% !important;
        }

        .hero-title {
          font-size: 20px !important;
          font-weight: 700 !important;
          color: #1e3a8a !important;
          margin: 0 0 15px 0 !important;
          line-height: 1.2 !important;
        }

        .hero-subtitle {
          font-size: 14px !important;
          color: #4b5563 !important;
          margin: 0 0 20px 0 !important;
          line-height: 1.4 !important;
        }

        .hero-button {
          background-color: #2563eb !important;
          color: #ffffff !important;
          padding: 12px 20px !important;
          border-radius: 6px !important;
          text-decoration: none !important;
          font-weight: 500 !important;
          font-size: 14px !important;
          display: inline-block !important;
        }

        /* PROGRAMS SECTION MOBILE */
        .programs-section {
          padding: 30px 15px !important;
          background-color: #ffffff !important;
          width: 100% !important;
        }

        .section-title {
          text-align: center !important;
          font-size: 20px !important;
          font-weight: 700 !important;
          color: #1f2937 !important;
          margin: 0 0 25px 0 !important;
        }

        .program-card-container {
          display: flex !important;
          flex-direction: column !important;
          gap: 15px !important;
          width: 100% !important;
        }

        .program-card {
          background-color: #f9fafb !important;
          border: 1px solid #e5e7eb !important;
          border-radius: 8px !important;
          padding: 20px 15px !important;
          text-align: center !important;
          width: 100% !important;
        }

        .program-card h3 {
          font-size: 16px !important;
          font-weight: 600 !important;
          color: #1f2937 !important;
          margin: 10px 0 8px 0 !important;
        }

        .program-card p {
          font-size: 13px !important;
          color: #6b7280 !important;
          line-height: 1.4 !important;
          margin: 0 !important;
        }

        /* NEWS SECTION MOBILE */
        .news-section {
          padding: 30px 15px !important;
          background-color: #ffffff !important;
          border-top: 1px solid #e5e7eb !important;
          width: 100% !important;
        }

        .news-card-container {
          display: flex !important;
          flex-direction: column !important;
          gap: 15px !important;
          width: 100% !important;
        }

        .news-card {
          background-color: #ffffff !important;
          border: 1px solid #e5e7eb !important;
          border-radius: 8px !important;
          overflow: hidden !important;
          width: 100% !important;
        }

        .news-image {
          width: 100% !important;
          height: 150px !important;
          object-fit: cover !important;
        }

        .news-content {
          padding: 15px !important;
        }

        .news-title {
          font-size: 14px !important;
          font-weight: 600 !important;
          color: #1f2937 !important;
          margin: 0 0 8px 0 !important;
        }

        .news-excerpt {
          font-size: 12px !important;
          color: #4b5563 !important;
          line-height: 1.4 !important;
          margin: 0 0 10px 0 !important;
        }

        /* CTA SECTION MOBILE */
        .cta-section {
          background-color: #1e3a8a !important;
          color: #ffffff !important;
          padding: 30px 15px !important;
          text-align: center !important;
          width: 100% !important;
        }

        .cta-section h2 {
          font-size: 20px !important;
          font-weight: 700 !important;
          margin: 0 0 15px 0 !important;
        }

        .cta-section p {
          font-size: 14px !important;
          margin: 0 0 20px 0 !important;
          line-height: 1.4 !important;
        }

        .cta-buttons-container {
          display: flex !important;
          flex-direction: column !important;
          gap: 10px !important;
          width: 100% !important;
        }

        .cta-main-button,
        .cta-secondary-button {
          width: 100% !important;
          padding: 12px 20px !important;
          border-radius: 6px !important;
          text-decoration: none !important;
          font-weight: 500 !important;
          font-size: 14px !important;
          text-align: center !important;
          display: block !important;
        }

        .cta-main-button {
          background-color: #ffffff !important;
          color: #1e3a8a !important;
        }

        .cta-secondary-button {
          background-color: transparent !important;
          color: #ffffff !important;
          border: 2px solid #ffffff !important;
        }

        /* FOOTER MOBILE */
        .site-footer {
          background-color: #111827 !important;
          color: #d1d5db !important;
          padding: 30px 15px 0 15px !important;
          width: 100% !important;
        }

        .footer-container {
          display: flex !important;
          flex-direction: column !important;
          gap: 20px !important;
          width: 100% !important;
          padding-bottom: 20px !important;
        }

        .footer-container h3 {
          color: #ffffff !important;
          font-size: 16px !important;
          margin: 0 0 10px 0 !important;
        }

        .footer-bottom {
          border-top: 1px solid #374151 !important;
          text-align: center !important;
          padding: 15px 0 !important;
          font-size: 12px !important;
        }
      }
    </style>

    <?!= include('Stylesheet'); ?>

    <!-- CSS OVERRIDE FINAL - PRIORITAS TERTINGGI -->
    <style>
      /* OVERRIDE SEMUA CSS DENGAN PRIORITAS TERTINGGI */
      @media screen and (max-width: 768px) {
        html, body {
          width: 100vw !important;
          max-width: 100vw !important;
          overflow-x: hidden !important;
          font-size: 14px !important;
        }

        .site-header {
          padding: 0 10px !important;
        }

        .header-container {
          flex-direction: column !important;
          text-align: center !important;
          padding: 10px 0 !important;
        }

        .nav-links {
          flex-direction: row !important;
          flex-wrap: wrap !important;
          justify-content: center !important;
          gap: 8px !important;
          margin: 10px 0 !important;
        }

        .hero-title {
          font-size: 20px !important;
          line-height: 1.2 !important;
        }

        .program-card-container,
        .news-card-container {
          flex-direction: column !important;
          gap: 15px !important;
        }

        .cta-buttons-container {
          flex-direction: column !important;
          gap: 10px !important;
        }

        .footer-container {
          flex-direction: column !important;
          gap: 20px !important;
        }
      }

      /* FORCE MOBILE PADA SEMUA KONDISI */
      @media screen and (max-device-width: 768px) {
        * {
          max-width: 100% !important;
        }

        .program-card-container,
        .news-card-container,
        .cta-buttons-container,
        .footer-container {
          display: flex !important;
          flex-direction: column !important;
        }
      }
    </style>
  </head>
  <body>
    <!-- HEADER -->
    <header class="site-header">
      <div class="header-container">
        <div class="logo">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Relawan TIK Kaltim</a>
        </div>
        <nav class="nav-links">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a>
        </nav>
        <div class="header-cta">
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=gabung_relawan" class="cta-button">Gabung Relawan!</a>
        </div>
      </div>
    </header>

    <main>
      <!-- HERO SECTION -->
      <section class="hero-section">
          <h1 class="hero-title">Mengawal Literasi Digital di Benua Etam</h1>
          <p class="hero-subtitle">Menjadi garda terdepan dalam edukasi, inovasi, dan kolaborasi untuk mewujudkan masyarakat Kalimantan Timur yang cerdas dan cakap digital.</p>
          <a href="<?= ScriptApp.getService().getUrl() ?>?page=program" class="hero-button">Jelajahi Program Kami</a>
      </section>

      <!-- PROGRAMS SECTION -->
      <section class="programs-section">
        <h2 class="section-title">Program dan Kegiatan Utama Kami</h2>
        <div class="program-card-container">
            <article class="program-card">
              <div class="program-icon"><svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 256 256"><path d="M208,32H48A16,16,0,0,0,32,48V176a16,16,0,0,0,16,16H96v16H72a8,8,0,0,0,0,16H184a8,8,0,0,0,0-16H160V192h48a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM48,48H208V176H48Z"></path></svg></div>
              <h3>Edukasi & Literasi Digital</h3>
              <p>Sosialisasi internet sehat, anti-hoax, dan privasi data untuk pelajar dan masyarakat umum.</p>
            </article>
            <article class="program-card">
              <div class="program-icon"><svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 256 256"><path d="M240,94.39,229.41,83.8A16,16,0,0,0,208,83.8V48a16,16,0,0,0-16-16H64A16,16,0,0,0,48,48V83.8a16,16,0,0,0-21.41.06L16,94.39A16,16,0,0,0,16,118.8L34.13,137H221.87L240,118.8A16,16,0,0,0,240,94.39ZM64,48H192V80H64Zm156.4,62.8L202.13,129H53.87L35.6,110.8a.72.72,0,0,1,0-.06,2,2,0,0,1-1-1.46,1,1,0,0,1,.05-.4,2,2,0,0,1,.24-.59l.06,0L34.8,109,53.05,90.72a16.1,16.1,0,0,0,10.27-1.11,15.87,15.87,0,0,0-10.27-29.61V48h144V60a15.87,15.87,0,0,0-10.27,29.61,16.1,16.1,0,0,0,10.27,1.11L221.2,109l-.15.15.06,0,.24.59a2,2,0,0,1,.05,.4,1,1,0,0,1-1,1.46,2,2,0,0,1,0,.06ZM128,152a16,16,0,0,0-16,16v40a8,8,0,0,0,16,0V168A16,16,0,0,0,128,152Zm-48,16a16,16,0,0,0-16,16v24a8,8,0,0,0,16,0V184A16,16,0,0,0,80,168Zm96,0a16,16,0,0,0-16,16v24a8,8,0,0,0,16,0V184A16,16,0,0,0,176,168Z"></path></svg></div>
              <h3>Pendampingan UMKM</h3>
              <p>Membantu UMKM go-digital, optimalisasi media sosial, dan pemanfaatan teknologi untuk usaha.</p>
            </article>
            <article class="program-card">
              <div class="program-icon"><svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 256 256"><path d="M128,24a104,104,0,1,0,104,104A104.11,104.11,0,0,0,128,24ZM74.08,197.5a64,64,0,0,1,107.84,0,88.11,88.11,0,0,1-107.84,0ZM96,120a32,32,0,1,1,32,32A32,32,0,0,1,96,120Zm97.76,66.41a79.66,79.66,0,0,0-36.25-28.73,48,48,0,1,0-63.02,0,79.66,79.66,0,0,0-36.25,28.73,88,88,0,1,1,135.52,0Z"></path></svg></div>
              <h3>Pengembangan Relawan</h3>
              <p>Pelatihan internal, workshop, dan sertifikasi untuk meningkatkan keahlian para relawan TIK.</p>
            </article>
        </div>
      </section>

      <!-- NEWS SECTION (SEKARANG DINAMIS) -->
      <section class="news-section">
        <h2 class="section-title">Informasi Terkini dari RTIK Kaltim</h2>
        <div class="news-card-container">
          <? if (typeof latestNews !== 'undefined' && latestNews.length > 0) { ?>
            <? for (var i = 0; i < latestNews.length; i++) { ?>
              <? var article = latestNews[i]; ?>
              <article class="news-card">
                  <img src="<?= article.gambar_url ?>" alt="Gambar untuk artikel: <?= article.judul ?>" class="news-image">
                  <div class="news-content">
                      <p class="news-meta">Kategori: <?= article.kategori ?></p>
                      <h3 class="news-title"><?= article.judul ?></h3>
                      <p class="news-excerpt"><?= article.kutipan ?></p>
                      <a href="<?= ScriptApp.getService().getUrl() ?>?page=artikel&id=<?= article.id ?>" class="read-more-link">Baca Selengkapnya →</a>
                  </div>
              </article>
            <? } ?>
          <? } else { ?>
            <p style="text-align: center; width: 100%;">Belum ada berita terbaru.</p>
          <? } ?>
        </div>
      </section>
      
      <!-- CTA SECTION -->
      <section class="cta-section">
        <h2>Mari Berkolaborasi!</h2>
        <p>Jadilah bagian dari perubahan atau ajak kami untuk berkolaborasi dalam event literasi digital Anda.</p>
        <div class="cta-buttons-container">
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=gabung_relawan" class="cta-main-button">Saya Ingin Jadi Relawan</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=kemitraan" class="cta-secondary-button">Ajukan Kemitraan</a>
        </div>
      </section>
    </main>
    
    <!-- FOOTER -->
    <footer class="site-footer">
      <div class="footer-container">
        <div class="footer-about"><h3>Relawan TIK Kaltim</h3><p>Organisasi sosial nirlaba yang mendedikasikan diri untuk akselerasi dan penetrasi literasi digital di Kalimantan Timur.</p></div>
        <div class="footer-links"><h3>Navigasi</h3><ul><li><a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a></li></ul></div>
        <div class="footer-contact"><h3>Hubungi Kami</h3><p>Email: <EMAIL></p><p>Sekretariat: [Alamat Sekretariat]</p></div>
        <div class="footer-social"><h3>Ikuti Kami</h3><div class="social-icons"><a href="#">[FB]</a> <a href="#">[IG]</a> <a href="#">[TW]</a> <a href="#">[YT]</a></div></div>
      </div>
      <div class="footer-bottom"><p>© 2025 Relawan TIK Provinsi Kalimantan Timur. Hak Cipta Dilindungi.</p></div>
    </footer>

    <script>
      // SCRIPT SUPER AGRESIF untuk memaksa mobile layout
      (function() {
        // Deteksi mobile device
        function isMobileDevice() {
          return window.innerWidth <= 768 ||
                 window.screen.width <= 768 ||
                 /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        // Deteksi apakah di dalam iframe
        function isInIframe() {
          try {
            return window.self !== window.top;
          } catch (e) {
            return true;
          }
        }

        // Force viewport meta tag
        function forceViewport() {
          // Hapus semua viewport yang ada
          var viewports = document.querySelectorAll('meta[name="viewport"]');
          viewports.forEach(function(vp) { vp.remove(); });

          // Buat viewport baru dengan force mobile
          var viewport = document.createElement('meta');
          viewport.name = 'viewport';
          viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no';
          document.head.insertBefore(viewport, document.head.firstChild);

          // Jika di iframe, coba set viewport di parent
          if (isInIframe()) {
            try {
              var parentViewports = window.parent.document.querySelectorAll('meta[name="viewport"]');
              parentViewports.forEach(function(vp) { vp.remove(); });

              var parentViewport = window.parent.document.createElement('meta');
              parentViewport.name = 'viewport';
              parentViewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
              window.parent.document.head.appendChild(parentViewport);
            } catch (e) {
              console.log('Cannot access parent frame');
            }
          }
        }

        // FORCE MOBILE LAYOUT - LANGSUNG MANIPULASI DOM
        function forceMobileLayout() {
          if (isMobileDevice()) {
            console.log('Mobile device detected, forcing mobile layout...');

            // Force body styles
            document.body.style.cssText = `
              width: 100vw !important;
              max-width: 100vw !important;
              overflow-x: hidden !important;
              margin: 0 !important;
              padding: 0 !important;
              font-family: 'Inter', sans-serif !important;
              background-color: #f9fafb !important;
              font-size: 14px !important;
            `;

            // Force header mobile layout
            var header = document.querySelector('.site-header');
            if (header) {
              header.style.cssText = `
                width: 100% !important;
                padding: 0 10px !important;
                background-color: #ffffff !important;
                border-bottom: 1px solid #e5e7eb !important;
              `;
            }

            var headerContainer = document.querySelector('.header-container');
            if (headerContainer) {
              headerContainer.style.cssText = `
                display: flex !important;
                flex-direction: column !important;
                width: 100% !important;
                text-align: center !important;
                padding: 10px 0 !important;
                height: auto !important;
              `;
            }

            var navLinks = document.querySelector('.nav-links');
            if (navLinks) {
              navLinks.style.cssText = `
                display: flex !important;
                flex-wrap: wrap !important;
                justify-content: center !important;
                gap: 8px !important;
                margin: 10px 0 !important;
                width: 100% !important;
              `;

              // Force nav link styles
              var links = navLinks.querySelectorAll('a');
              links.forEach(function(link) {
                link.style.cssText = `
                  font-size: 12px !important;
                  padding: 6px 10px !important;
                  color: #4b5563 !important;
                  text-decoration: none !important;
                  border-radius: 4px !important;
                  background-color: #f3f4f6 !important;
                `;
              });
            }

            // Force hero section mobile
            var heroSection = document.querySelector('.hero-section');
            if (heroSection) {
              heroSection.style.cssText = `
                background-color: #e0e7ff !important;
                text-align: center !important;
                padding: 30px 15px !important;
                width: 100% !important;
              `;
            }

            var heroTitle = document.querySelector('.hero-title');
            if (heroTitle) {
              heroTitle.style.cssText = `
                font-size: 20px !important;
                font-weight: 700 !important;
                color: #1e3a8a !important;
                margin: 0 0 15px 0 !important;
                line-height: 1.2 !important;
              `;
            }

            // Force program cards mobile
            var programContainer = document.querySelector('.program-card-container');
            if (programContainer) {
              programContainer.style.cssText = `
                display: flex !important;
                flex-direction: column !important;
                gap: 15px !important;
                width: 100% !important;
              `;

              var programCards = programContainer.querySelectorAll('.program-card');
              programCards.forEach(function(card) {
                card.style.cssText = `
                  background-color: #f9fafb !important;
                  border: 1px solid #e5e7eb !important;
                  border-radius: 8px !important;
                  padding: 20px 15px !important;
                  text-align: center !important;
                  width: 100% !important;
                `;
              });
            }

            // Force news cards mobile
            var newsContainer = document.querySelector('.news-card-container');
            if (newsContainer) {
              newsContainer.style.cssText = `
                display: flex !important;
                flex-direction: column !important;
                gap: 15px !important;
                width: 100% !important;
              `;

              var newsCards = newsContainer.querySelectorAll('.news-card');
              newsCards.forEach(function(card) {
                card.style.cssText = `
                  background-color: #ffffff !important;
                  border: 1px solid #e5e7eb !important;
                  border-radius: 8px !important;
                  overflow: hidden !important;
                  width: 100% !important;
                `;
              });
            }

            // Force CTA buttons mobile
            var ctaContainer = document.querySelector('.cta-buttons-container');
            if (ctaContainer) {
              ctaContainer.style.cssText = `
                display: flex !important;
                flex-direction: column !important;
                gap: 10px !important;
                width: 100% !important;
              `;

              var ctaButtons = ctaContainer.querySelectorAll('a');
              ctaButtons.forEach(function(btn) {
                btn.style.cssText += `
                  width: 100% !important;
                  text-align: center !important;
                  display: block !important;
                  padding: 12px 20px !important;
                `;
              });
            }
          }
        }

        // Force no horizontal scroll
        function preventHorizontalScroll() {
          document.documentElement.style.overflowX = 'hidden';
          document.body.style.overflowX = 'hidden';

          // Jika di iframe, coba set di parent juga
          if (isInIframe()) {
            try {
              window.parent.document.documentElement.style.overflowX = 'hidden';
              window.parent.document.body.style.overflowX = 'hidden';
            } catch (e) {
              console.log('Cannot access parent frame');
            }
          }
        }

        // Initialize semua fungsi
        function init() {
          console.log('Initializing mobile layout...');
          console.log('Window width:', window.innerWidth);
          console.log('Screen width:', window.screen.width);
          console.log('User agent:', navigator.userAgent);

          forceViewport();
          forceMobileLayout();
          preventHorizontalScroll();

          // Log hasil
          console.log('Mobile layout applied!');
        }

        // Jalankan SEGERA
        init();

        // Jalankan saat DOM ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', init);
        }

        // Jalankan saat window load
        window.addEventListener('load', init);

        // Jalankan ulang saat resize
        window.addEventListener('resize', function() {
          console.log('Window resized, re-applying mobile layout...');
          forceMobileLayout();
          preventHorizontalScroll();
        });

        // Jalankan ulang saat orientationchange
        window.addEventListener('orientationchange', function() {
          setTimeout(function() {
            console.log('Orientation changed, re-applying mobile layout...');
            init();
          }, 100);
        });

        // Jalankan ulang dengan interval untuk memastikan
        setTimeout(init, 50);
        setTimeout(init, 100);
        setTimeout(init, 200);
        setTimeout(init, 500);
        setTimeout(init, 1000);
        setTimeout(init, 2000);

        // Jalankan setiap 3 detik untuk 15 detik pertama (untuk memastikan)
        var attempts = 0;
        var forceInterval = setInterval(function() {
          attempts++;
          console.log('Force attempt #' + attempts);
          init();

          if (attempts >= 5) {
            clearInterval(forceInterval);
            console.log('Force attempts completed');
          }
        }, 3000);
      })();
    </script>

  </body>
</html>


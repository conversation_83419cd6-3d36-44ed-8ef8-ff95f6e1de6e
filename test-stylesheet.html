<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Test Stylesheet Responsive</title>
    
    <!-- Include Stylesheet.html -->
    <?!= include('Stylesheet'); ?>
    
    <style>
        /* Test indicator */
        .test-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #dc2626;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 9999;
        }
        
        @media screen and (max-width: 768px) {
            .test-indicator {
                background: #16a34a;
            }
            .test-indicator::after {
                content: " - MOBILE CSS ACTIVE";
            }
        }
        
        .debug-info {
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-indicator">CSS TEST</div>
    
    <!-- HEADER TEST -->
    <header class="site-header">
        <div class="header-container">
            <div class="logo">
                <a href="#">Relawan TIK Kaltim</a>
            </div>
            <nav class="nav-links">
                <a href="#">Beranda</a>
                <a href="#">Tentang Kami</a>
                <a href="#">Program</a>
                <a href="#">Berita</a>
            </nav>
            <div class="header-cta">
                <a href="#" class="cta-button">Gabung Relawan!</a>
            </div>
        </div>
    </header>

    <main>
        <!-- HERO TEST -->
        <section class="hero-section">
            <h1 class="hero-title">Test Hero Title</h1>
            <p class="hero-subtitle">Test hero subtitle untuk mengecek responsivitas</p>
            <a href="#" class="hero-button">Test Button</a>
        </section>

        <!-- PROGRAM CARDS TEST -->
        <section class="programs-section">
            <h2 class="section-title">Test Program Cards</h2>
            <div class="program-card-container">
                <article class="program-card">
                    <h3>Card 1</h3>
                    <p>Test program card 1</p>
                </article>
                <article class="program-card">
                    <h3>Card 2</h3>
                    <p>Test program card 2</p>
                </article>
                <article class="program-card">
                    <h3>Card 3</h3>
                    <p>Test program card 3</p>
                </article>
            </div>
        </section>

        <!-- NEWS CARDS TEST -->
        <section class="news-section">
            <h2 class="section-title">Test News Cards</h2>
            <div class="news-card-container">
                <article class="news-card">
                    <img src="https://placehold.co/300x200/dbeafe/1e3a8a?text=News1" alt="News 1" class="news-image">
                    <div class="news-content">
                        <h3 class="news-title">News Title 1</h3>
                        <p class="news-excerpt">Test news excerpt 1</p>
                    </div>
                </article>
                <article class="news-card">
                    <img src="https://placehold.co/300x200/dbeafe/1e3a8a?text=News2" alt="News 2" class="news-image">
                    <div class="news-content">
                        <h3 class="news-title">News Title 2</h3>
                        <p class="news-excerpt">Test news excerpt 2</p>
                    </div>
                </article>
            </div>
        </section>

        <!-- CTA TEST -->
        <section class="cta-section">
            <h2>Test CTA Section</h2>
            <p>Test CTA description</p>
            <div class="cta-buttons-container">
                <a href="#" class="cta-main-button">Main Button</a>
                <a href="#" class="cta-secondary-button">Secondary Button</a>
            </div>
        </section>
    </main>

    <!-- DEBUG INFO -->
    <div class="debug-info" id="debug-info">
        Loading debug info...
    </div>

    <script>
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debug-info');
            const isMobile = window.innerWidth <= 768;
            const mediaQuery = window.matchMedia('(max-width: 768px)').matches;
            
            // Check computed styles
            const headerContainer = document.querySelector('.header-container');
            const programContainer = document.querySelector('.program-card-container');
            const newsContainer = document.querySelector('.news-card-container');
            
            const headerDirection = headerContainer ? window.getComputedStyle(headerContainer).flexDirection : 'unknown';
            const programDirection = programContainer ? window.getComputedStyle(programContainer).flexDirection : 'unknown';
            const newsDirection = newsContainer ? window.getComputedStyle(newsContainer).flexDirection : 'unknown';
            
            debugInfo.innerHTML = `
                <strong>STYLESHEET.HTML TEST RESULTS:</strong><br>
                Window Width: ${window.innerWidth}px<br>
                Screen Width: ${window.screen.width}px<br>
                Should be Mobile: ${isMobile ? 'YES' : 'NO'}<br>
                Media Query Active: ${mediaQuery ? 'YES' : 'NO'}<br>
                Header Flex Direction: ${headerDirection}<br>
                Program Cards Direction: ${programDirection}<br>
                News Cards Direction: ${newsDirection}<br>
                <br>
                <strong>Expected on Mobile:</strong><br>
                - Header: column (not row)<br>
                - Program Cards: column (not row)<br>
                - News Cards: column (not row)<br>
                - Green indicator (not red)<br>
                <br>
                <strong>Status:</strong> ${
                    isMobile && mediaQuery && 
                    headerDirection === 'column' && 
                    programDirection === 'column' && 
                    newsDirection === 'column' 
                    ? '✅ RESPONSIVE WORKING!' 
                    : '❌ NOT RESPONSIVE'
                }
            `;
        }
        
        // Update on load and resize
        updateDebugInfo();
        window.addEventListener('resize', updateDebugInfo);
        window.addEventListener('orientationchange', function() {
            setTimeout(updateDebugInfo, 100);
        });
        
        // Auto update every 2 seconds
        setInterval(updateDebugInfo, 2000);
    </script>
</body>
</html>

# Instruksi Deploy Perubahan Font Size Mobile

## Perubahan yang Dibuat
Saya telah memperbesar font size untuk mobile dengan perubahan berikut di `Stylesheet.html`:

1. **Font size dasar HTML**: Dinaikkan dari 16px ke 20px
2. **Text size adjust**: Dinaikkan ke 150% untuk mobile
3. **Zoom**: Ditambahkan zoom 1.2x untuk mobile
4. **Transform scale**: Ditambahkan scale 1.1x untuk mobile
5. **Font size mobile**: Semua text dipaksa minimum 20px
6. **Heading mobile**: Dipaksa minimum 28px
7. **Button mobile**: Dipaksa 22px dengan padding lebih besar

## Cara Deploy

### Opsi 1: Copy Manual ke Google Apps Script Editor
1. Buka Google Apps Script Editor di browser
2. Pilih file `Stylesheet.html`
3. Copy seluruh isi file `Stylesheet.html` dari folder ini
4. Paste ke Google Apps Script Editor
5. Save (Ctrl+S)
6. Deploy ulang web app

### Opsi 2: Jika Ada Clasp (Command Line)
```bash
# Install clasp jika belum ada
npm install -g @google/clasp

# Login ke Google
clasp login

# Push perubahan
clasp push

# Deploy
clasp deploy
```

## Hasil yang Diharapkan
- Font di mobile akan terlihat jauh lebih besar
- Text akan lebih mudah dibaca di smartphone
- Layout tetap responsive
- Zoom dan scale akan membuat semua elemen lebih besar

## Jika Masih Kecil
Jika font masih terasa kecil, bisa dinaikkan lagi dengan mengubah:
- `font-size: 22px` menjadi `font-size: 24px` atau lebih
- `zoom: 1.2` menjadi `zoom: 1.4` atau lebih
- `transform: scale(1.1)` menjadi `scale(1.3)` atau lebih

## Testing
Setelah deploy, test di smartphone dengan:
1. Buka URL web app di browser mobile
2. Cek apakah font sudah cukup besar
3. Scroll dan pastikan layout tidak rusak

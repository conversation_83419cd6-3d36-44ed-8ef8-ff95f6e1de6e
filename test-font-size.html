<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <title>Test Font Size</title>
    
    <!-- Include Stylesheet.html -->
    <?!= include('Stylesheet'); ?>
    
    <style>
        .test-container {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .font-test {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #fbbf24;
        }
        
        .size-indicator {
            background: rgba(34, 197, 94, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            display: inline-block;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📏 Test Font Size Mobile</h1>
        <p>Tool untuk mengecek ukuran font di mobile</p>
        
        <div class="test-section">
            <h2>🎯 Font Size Test</h2>
            
            <div class="font-test">
                <h1 class="hero-title">Hero Title Test</h1>
                <span class="size-indicator" id="hero-title-size">Loading...</span>
            </div>
            
            <div class="font-test">
                <h2 class="section-title">Section Title Test</h2>
                <span class="size-indicator" id="section-title-size">Loading...</span>
            </div>
            
            <div class="font-test">
                <p class="hero-subtitle">Hero subtitle test untuk mengecek readability</p>
                <span class="size-indicator" id="hero-subtitle-size">Loading...</span>
            </div>
            
            <div class="font-test">
                <div class="nav-links">
                    <a href="#">Nav Link Test</a>
                </div>
                <span class="size-indicator" id="nav-link-size">Loading...</span>
            </div>
            
            <div class="font-test">
                <div class="program-card">
                    <h3>Program Card Title</h3>
                    <p>Program card description untuk test readability di mobile device</p>
                </div>
                <span class="size-indicator" id="program-card-size">Loading...</span>
            </div>
            
            <div class="font-test">
                <div class="news-card">
                    <h3 class="news-title">News Title Test</h3>
                    <p class="news-excerpt">News excerpt test untuk mengecek apakah text readable di smartphone</p>
                </div>
                <span class="size-indicator" id="news-card-size">Loading...</span>
            </div>
            
            <div class="font-test">
                <a href="#" class="cta-main-button">CTA Button Test</a>
                <span class="size-indicator" id="cta-button-size">Loading...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📱 Device Info</h2>
            <div id="device-info">Loading...</div>
        </div>
        
        <div class="test-section">
            <h2>✅ Font Size Recommendations</h2>
            <ul style="line-height: 1.8;">
                <li><strong>Hero Title:</strong> 28px+ (readable dari jarak normal)</li>
                <li><strong>Section Title:</strong> 24px+ (jelas dan prominent)</li>
                <li><strong>Body Text:</strong> 16px+ (comfortable reading)</li>
                <li><strong>Navigation:</strong> 16px+ (easy to tap)</li>
                <li><strong>Buttons:</strong> 16px+ (clear call-to-action)</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2>🚀 Test Actions</h2>
            <div style="text-align: center;">
                <a href="Index.html" class="cta-main-button">Test Index.html</a>
            </div>
        </div>
    </div>

    <script>
        function getFontSize(element) {
            if (!element) return 'N/A';
            const computedStyle = window.getComputedStyle(element);
            return computedStyle.fontSize;
        }
        
        function updateFontSizes() {
            // Get font sizes
            const heroTitle = document.querySelector('.hero-title');
            const sectionTitle = document.querySelector('.section-title');
            const heroSubtitle = document.querySelector('.hero-subtitle');
            const navLink = document.querySelector('.nav-links a');
            const programCardH3 = document.querySelector('.program-card h3');
            const programCardP = document.querySelector('.program-card p');
            const newsTitle = document.querySelector('.news-title');
            const newsExcerpt = document.querySelector('.news-excerpt');
            const ctaButton = document.querySelector('.cta-main-button');
            
            // Update indicators
            document.getElementById('hero-title-size').textContent = getFontSize(heroTitle);
            document.getElementById('section-title-size').textContent = getFontSize(sectionTitle);
            document.getElementById('hero-subtitle-size').textContent = getFontSize(heroSubtitle);
            document.getElementById('nav-link-size').textContent = getFontSize(navLink);
            document.getElementById('program-card-size').textContent = 
                'H3: ' + getFontSize(programCardH3) + ', P: ' + getFontSize(programCardP);
            document.getElementById('news-card-size').textContent = 
                'Title: ' + getFontSize(newsTitle) + ', Text: ' + getFontSize(newsExcerpt);
            document.getElementById('cta-button-size').textContent = getFontSize(ctaButton);
            
            // Device info
            const isMobile = window.innerWidth <= 768;
            document.getElementById('device-info').innerHTML = `
                <strong>Window:</strong> ${window.innerWidth} x ${window.innerHeight}px<br>
                <strong>Screen:</strong> ${window.screen.width} x ${window.screen.height}px<br>
                <strong>Device Pixel Ratio:</strong> ${window.devicePixelRatio}<br>
                <strong>Is Mobile:</strong> ${isMobile ? 'YES' : 'NO'}<br>
                <strong>User Agent:</strong> ${navigator.userAgent.includes('Mobile') ? 'Mobile' : 'Desktop'}
            `;
        }
        
        // Update on load and resize
        window.addEventListener('load', updateFontSizes);
        window.addEventListener('resize', updateFontSizes);
        
        // Initial update
        setTimeout(updateFontSizes, 100);
    </script>
</body>
</html>

<style>
  /* =================================== */
  /* FORCE MOBILE RESPONSIVE - PRIORITAS TERTINGGI */
  /* =================================== */

  /* Impor font Inter dari Google Fonts */
  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

  /* FORCE RESPONSIVE BEHAVIOR - KHUSUS UNTUK GOOGLE APPS SCRIPT */
  html {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
    font-size: 16px !important;
    -webkit-text-size-adjust: 100% !important;
    -ms-text-size-adjust: 100% !important;
    box-sizing: border-box !important;
  }

  * {
    box-sizing: border-box !important;
  }

  *:before, *:after {
    box-sizing: border-box !important;
  }

  /* MOBILE FIRST - FORCE MOBILE LAYOUT */
  @media screen and (max-width: 768px),
         screen and (max-device-width: 768px),
         only screen and (max-width: 768px) {

    html, body {
      width: 100vw !important;
      max-width: 100vw !important;
      overflow-x: hidden !important;
      font-size: 16px !important;
    }

    /* FORCE HEADER MOBILE */
    .site-header {
      padding: 0 10px !important;
      width: 100% !important;
    }

    .header-container {
      display: flex !important;
      flex-direction: column !important;
      text-align: center !important;
      padding: 10px 0 !important;
      height: auto !important;
      width: 100% !important;
    }

    .nav-links {
      display: flex !important;
      flex-wrap: wrap !important;
      justify-content: center !important;
      gap: 8px !important;
      margin: 10px 0 !important;
      width: 100% !important;
    }

    .nav-links a {
      font-size: 16px !important;
      padding: 8px 12px !important;
    }

    /* FORCE SECTIONS MOBILE */
    .hero-section,
    .programs-section,
    .news-section,
    .about-section,
    .program-detail-section,
    .news-list-section,
    .form-section,
    .article-detail-section,
    .cta-section,
    .site-footer {
      padding: 30px 15px !important;
      width: 100% !important;
    }

    .hero-title {
      font-size: 28px !important;
      line-height: 1.3 !important;
    }

    .section-title {
      font-size: 24px !important;
    }

    /* FORCE CONTAINERS MOBILE */
    .program-card-container,
    .news-card-container,
    .profile-card-container,
    .cta-buttons-container,
    .footer-container {
      display: flex !important;
      flex-direction: column !important;
      gap: 15px !important;
      width: 100% !important;
    }

    .news-list-container {
      display: grid !important;
      grid-template-columns: 1fr !important;
      gap: 15px !important;
      width: 100% !important;
    }

    /* FORCE CARDS MOBILE */
    .program-card,
    .news-card {
      width: 100% !important;
      margin: 0 !important;
    }

    /* FORCE BUTTONS MOBILE */
    .cta-main-button,
    .cta-secondary-button,
    .hero-button {
      width: 100% !important;
      text-align: center !important;
      display: block !important;
      font-size: 16px !important;
      padding: 12px 20px !important;
    }

    /* FORCE TEXT SIZES MOBILE */
    .hero-subtitle {
      font-size: 18px !important;
      line-height: 1.4 !important;
    }

    .program-card h3 {
      font-size: 20px !important;
    }

    .program-card p {
      font-size: 16px !important;
      line-height: 1.5 !important;
    }

    .news-title {
      font-size: 18px !important;
    }

    .news-excerpt {
      font-size: 16px !important;
      line-height: 1.5 !important;
    }

    .cta-section h2 {
      font-size: 26px !important;
    }

    .cta-section p {
      font-size: 18px !important;
      line-height: 1.5 !important;
    }

    /* FORCE GENERAL TEXT MOBILE */
    p {
      font-size: 16px !important;
      line-height: 1.5 !important;
    }

    h1 {
      font-size: 28px !important;
    }

    h2 {
      font-size: 24px !important;
    }

    h3 {
      font-size: 20px !important;
    }
  }

  body {
    font-family: 'Inter', sans-serif !important;
    margin: 0 !important;
    padding: 0 !important;
    color: #333 !important;
    background-color: #f9fafb !important; /* Latar abu-abu sangat muda */
    line-height: 1.6 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    overflow-x: hidden !important; /* Mencegah horizontal scroll */
    width: 100vw !important;
    max-width: 100vw !important;
    min-height: 100vh !important;
    box-sizing: border-box !important;
  }

  /* FORCE RESPONSIVE - DETEKSI MOBILE DAN TABLET */
  @media screen and (max-width: 1024px) {
    html, body {
      width: 100vw !important;
      max-width: 100vw !important;
      overflow-x: hidden !important;
    }
  }

  /* FORCE MOBILE LAYOUT - PRIORITAS TINGGI */
  @media screen and (max-width: 768px) {
    html, body {
      width: 100vw !important;
      max-width: 100vw !important;
      overflow-x: hidden !important;
      font-size: 14px !important;
    }

    .site-header {
      padding: 0 15px !important;
      width: 100% !important;
    }

    .header-container {
      flex-direction: column !important;
      text-align: center !important;
      padding: 10px 0 !important;
      width: 100% !important;
    }

    .nav-links {
      flex-wrap: wrap !important;
      justify-content: center !important;
      gap: 8px !important;
      margin: 10px 0 !important;
      width: 100% !important;
    }

    .nav-links a {
      font-size: 13px !important;
      padding: 6px 10px !important;
    }

    .hero-section,
    .programs-section,
    .news-section,
    .about-section,
    .program-detail-section,
    .news-list-section,
    .form-section,
    .article-detail-section,
    .cta-section,
    .site-footer {
      padding: 30px 15px !important;
      width: 100% !important;
    }

    .program-card-container,
    .news-card-container,
    .profile-card-container,
    .cta-buttons-container {
      flex-direction: column !important;
      gap: 15px !important;
      width: 100% !important;
    }

    .form-container {
      width: calc(100% - 20px) !important;
      margin: 0 10px !important;
      padding: 20px 15px !important;
    }

    .form-group input,
    .form-group textarea {
      width: 100% !important;
      font-size: 16px !important;
      padding: 12px !important;
    }
  }

  /* Mencegah overflow horizontal pada semua elemen */
  *, *::before, *::after {
    max-width: 100%;
  }

  /* Container utama untuk membatasi lebar maksimum */
  .container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 15px;
  }

  /* Gambar responsive */
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  /* =================================== */
  /* GAYA UNTUK HEADER & NAVIGASI        */
  /* =================================== */
  .site-header {
    background-color: #ffffff;
    padding: 0 50px;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 1000;
    width: 100%;
    box-sizing: border-box;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    height: 80px;
    width: 100%;
    position: relative;
  }

  .logo a {
    font-size: 24px;
    font-weight: 700;
    color: #1e3a8a; /* Biru tua */
    text-decoration: none;
    white-space: nowrap;
  }

  .nav-links {
    display: flex;
    gap: 40px;
    align-items: center;
  }

  .nav-links a {
    text-decoration: none;
    color: #4b5563; /* Abu-abu tua */
    font-size: 16px;
    font-weight: 500;
    transition: color 0.3s;
    white-space: nowrap;
  }

  .nav-links a:hover {
    color: #1e3a8a;
  }

  .cta-button {
    background-color: #2563eb; /* Biru cerah */
    color: #ffffff;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
    white-space: nowrap;
    display: inline-block;
  }

  .cta-button:hover {
    background-color: #1d4ed8; /* Biru lebih gelap saat hover */
  }

  /* Mobile menu toggle (hidden by default) */
  .mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 24px;
    color: #1e3a8a;
    cursor: pointer;
    padding: 5px;
  }

  /* =================================== */
  /* GAYA UNTUK HERO SECTION             */
  /* =================================== */
  .hero-section {
    background-color: #e0e7ff; /* Biru muda lavender */
    text-align: center;
    padding: 100px 50px;
  }

  .hero-title {
    font-size: 48px;
    font-weight: 700;
    color: #1e3a8a;
    margin-top: 0;
    margin-bottom: 20px;
  }

  .hero-subtitle {
    font-size: 18px;
    color: #4b5563;
    max-width: 600px;
    margin: 0 auto 30px auto;
  }

  .hero-button {
    background-color: #2563eb;
    color: #ffffff;
    padding: 15px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    font-size: 18px;
    transition: background-color 0.3s;
  }

  .hero-button:hover {
    background-color: #1d4ed8;
  }

  /* =================================== */
  /* GAYA UNTUK SEMUA SECTION            */
  /* =================================== */
  .programs-section, .news-section, .about-section, .program-detail-section, .news-list-section, .form-section, .article-detail-section {
    padding: 80px 50px;
    background-color: #ffffff;
  }
  
  /* Memberi batas antar section putih */
  .news-section, .program-detail-section, .news-list-section, .form-section, .article-detail-section {
     border-top: 1px solid #e5e7eb;
  }
  
  .section-title {
    text-align: center;
    font-size: 36px;
    font-weight: 700;
    color: #1f2937;
    margin-top: 0;
    margin-bottom: 50px;
  }

  /* =================================== */
  /* GAYA UNTUK PROGRAM UNGGULAN         */
  /* =================================== */
  .program-card-container {
    display: flex;
    justify-content: center;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .program-card {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    flex-basis: 30%;
  }
  
  .program-icon {
    color: #2563eb;
    margin-bottom: 20px;
  }

  .program-card h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin-top: 0;
    margin-bottom: 10px;
  }

  .program-card p {
    font-size: 16px;
    color: #6b7280;
    line-height: 1.6;
  }

  /* =================================== */
  /* GAYA UNTUK BERITA & ARTIKEL         */
  /* =================================== */
  .news-card-container {
    display: flex;
    justify-content: center;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .news-card {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    flex-basis: 30%;
    transition: transform 0.3s, box-shadow 0.3s;
  }

  .news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .news-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  .news-content {
    padding: 24px;
  }

  .news-meta {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 8px;
  }

  .news-title {
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin-top: 0;
    margin-bottom: 12px;
  }

  .news-excerpt {
    font-size: 16px;
    color: #4b5563;
    line-height: 1.6;
  }
  
  .read-more-link {
    color: #2563eb;
    text-decoration: none;
    font-weight: 500;
  }
  
  .read-more-link:hover {
    text-decoration: underline;
  }

  /* =================================== */
  /* GAYA UNTUK CALL TO ACTION (CTA)     */
  /* =================================== */
  .cta-section {
    background-color: #1e3a8a;
    color: #ffffff;
    padding: 80px 50px;
    text-align: center;
  }

  .cta-section h2 {
    font-size: 36px;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 20px;
  }
  
  .cta-section p {
    font-size: 18px;
    max-width: 600px;
    margin: 0 auto 30px auto;
    opacity: 0.9;
  }

  .cta-buttons-container {
    display: flex;
    justify-content: center;
    gap: 20px;
  }

  .cta-main-button, .cta-secondary-button {
    padding: 15px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: all 0.3s;
  }

  .cta-main-button {
    background-color: #ffffff;
    color: #1e3a8a;
  }
  
  .cta-main-button:hover {
    background-color: #e5e7eb;
  }
  
  .cta-secondary-button {
    background-color: transparent;
    color: #ffffff;
    border: 2px solid #ffffff;
  }

  .cta-secondary-button:hover {
    background-color: #ffffff;
    color: #1e3a8a;
  }

  /* =================================== */
  /* GAYA UNTUK FOOTER                   */
  /* =================================== */
  .site-footer {
    background-color: #111827;
    color: #d1d5db;
    padding: 60px 50px 0 50px;
  }
  
  .footer-container {
    display: flex;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding-bottom: 40px;
    gap: 40px;
  }
  
  .footer-container h3 {
    color: #ffffff;
    font-size: 18px;
    margin-bottom: 20px;
  }

  .footer-about, .footer-links, .footer-contact, .footer-social {
    flex: 1;
  }

  .footer-about {
    max-width: 300px;
  }

  .footer-links ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .footer-links li {
    margin-bottom: 10px;
  }

  .footer-links a, .footer-contact p {
    color: #d1d5db;
    text-decoration: none;
  }

  .footer-links a:hover {
    color: #ffffff;
    text-decoration: underline;
  }
  
  .social-icons a {
    color: #ffffff;
    text-decoration: none;
    font-size: 20px;
    margin-right: 15px;
  }
  
  .footer-bottom {
    border-top: 1px solid #374151;
    text-align: center;
    padding: 20px 0;
    font-size: 14px;
  }
  
  /* =================================== */
  /* GAYA UNTUK HALAMAN GENERIC (HEADER) */
  /* =================================== */
  .page-header {
      background-color: #dbeafe;
      padding: 60px 50px;
      text-align: center;
  }

  .page-header h1 {
      margin: 0 0 10px 0;
      font-size: 42px;
      color: #1e3a8a;
  }

  .page-header p {
      margin: 0;
      font-size: 18px;
      color: #4b5563;
  }

  /* =================================== */
  /* GAYA UNTUK HALAMAN TENTANG KAMI     */
  /* =================================== */
  .about-container {
      max-width: 900px;
      margin: 0 auto;
  }
  
  .about-content {
      margin-bottom: 50px;
  }
  
  .about-content h2 {
      font-size: 28px;
      color: #1f2937;
      border-bottom: 2px solid #2563eb;
      padding-bottom: 10px;
      margin-bottom: 20px;
  }

  .about-content p, .about-content li {
      font-size: 16px;
      line-height: 1.8;
      color: #374151;
  }

  .about-content ul {
      list-style-type: disc;
      padding-left: 20px;
  }

  .profile-card-container {
      display: flex;
      justify-content: center;
      gap: 30px;
      margin-top: 30px;
  }

  .profile-card {
      text-align: center;
      flex-basis: 22%;
  }

  .profile-card img {
      width: 150px;
      height: 150px;
      border-radius: 50%;
      object-fit: cover;
      margin-bottom: 15px;
      border: 4px solid #dbeafe;
  }

  .profile-card h4 {
      margin: 0 0 5px 0;
      font-size: 18px;
      color: #1f2937;
  }

  .profile-card p {
      margin: 0;
      color: #6b7280;
  }

  /* =================================== */
  /* GAYA UNTUK HALAMAN PROGRAM          */
  /* =================================== */
  .program-detail-container {
      max-width: 1000px;
      margin: 0 auto;
  }

  .program-detail-item {
      display: flex;
      gap: 50px;
      align-items: center;
      margin-bottom: 80px;
  }

  .program-detail-item.reverse {
      flex-direction: row-reverse;
  }
  
  .program-detail-image {
      flex: 1;
  }

  .program-detail-image img {
      width: 100%;
      border-radius: 12px;
  }

  .program-detail-text {
      flex: 1;
  }
  
  .program-detail-text h2 {
      font-size: 28px;
      color: #1f2937;
      margin-top: 0;
  }

  .program-detail-text p, .program-detail-text li {
      font-size: 16px;
      line-height: 1.8;
      color: #374151;
  }

  .program-detail-text ul {
      list-style: none;
      padding: 0;
  }

  .program-detail-text li {
      padding-left: 25px;
      position: relative;
      margin-bottom: 10px;
  }

  .program-detail-text li::before {
      content: '✔';
      color: #2563eb;
      position: absolute;
      left: 0;
  }

  /* =================================== */
  /* GAYA UNTUK HALAMAN BERITA (LIST)    */
  /* =================================== */
  .news-list-section {
    background-color: #f9fafb;
  }

  .news-list-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr); 
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
  }

  /* =================================== */
  /* GAYA UNTUK HALAMAN FORMULIR         */
  /* =================================== */
  .form-section {
    background-color: #f9fafb;
  }

  .form-container {
    max-width: 700px;
    margin: 0 auto;
    background-color: #ffffff;
    padding: 40px;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
  }

  .form-container h2 {
    font-size: 28px;
    text-align: center;
    margin-top: 0;
  }
  
  .form-container p {
    text-align: center;
    margin-bottom: 30px;
    color: #6b7280;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .form-group input, .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px; /* Minimum 16px untuk mencegah zoom di iOS */
    font-family: 'Inter', sans-serif;
    box-sizing: border-box;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }
  
  .form-group input:focus, .form-group textarea:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
  }

  .form-group button {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    background-color: #2563eb;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .form-group button:hover {
    background-color: #1d4ed8;
  }
  
  .form-group button:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
  }
  
  #status-message {
    text-align: center;
    margin-top: 20px;
    font-weight: 500;
  }
  
  #status-message.success {
    color: #16a34a; /* Hijau */
  }

  #status-message.error {
    color: #dc2626; /* Merah */
  }

  /* =================================== */
  /* GAYA UNTUK HALAMAN DETAIL ARTIKEL   */
  /* =================================== */
  .article-detail-section {
    background-color: #ffffff;
  }

  .article-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .article-header {
    margin-bottom: 30px;
    text-align: center;
  }

  .article-meta {
    color: #6b7280;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .article-title {
    font-size: 42px;
    color: #1f2937;
    margin: 0;
    line-height: 1.2;
  }

  .article-image {
    width: 100%;
    max-height: 450px;
    object-fit: cover;
    border-radius: 12px;
    margin-bottom: 40px;
  }

  .article-content {
    font-size: 18px;
    line-height: 1.8;
    color: #374151;
  }


  /* ========================================================== */
  /* ATURAN RESPONSIVE (MEDIA QUERIES) UNTUK TAMPILAN MOBILE    */
  /* ========================================================== */

  /* Tablet Portrait */
  @media screen and (max-width: 1024px) {
    .site-header {
      padding: 0 30px;
    }

    .hero-section {
      padding: 80px 30px;
    }

    .programs-section, .news-section, .about-section, .program-detail-section, .news-list-section, .form-section, .article-detail-section, .cta-section, .site-footer {
      padding-left: 30px;
      padding-right: 30px;
    }

    .program-card-container, .news-card-container {
      gap: 20px;
    }

    .news-list-container {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }
  }

  /* Mobile Landscape & Tablet Portrait */
  @media screen and (max-width: 768px) {
    .site-header {
      padding: 0 20px;
    }

    .header-container {
      flex-direction: column;
      height: auto;
      padding: 15px 0;
      text-align: center;
      min-height: 70px;
    }

    .nav-links {
      margin: 15px 0;
      gap: 15px;
      flex-wrap: wrap;
      justify-content: center;
      font-size: 14px;
      width: 100%;
    }

    .nav-links a {
      padding: 8px 12px;
      border-radius: 4px;
      transition: all 0.3s;
    }

    .nav-links a:hover {
      background-color: #f3f4f6;
    }

    .logo a {
      font-size: 20px;
      margin-bottom: 5px;
    }

    .header-cta {
      margin-top: 10px;
    }

    .cta-button {
      padding: 10px 20px;
      font-size: 14px;
    }

    /* --- SECTION & PADDING --- */
    .hero-section {
      padding: 60px 20px;
    }

    .programs-section, .news-section, .about-section, .program-detail-section, .news-list-section, .form-section, .article-detail-section, .cta-section, .site-footer {
      padding: 60px 20px;
    }

    /* --- FONT SIZES --- */
    .hero-title {
      font-size: 28px;
      line-height: 1.2;
      margin-bottom: 15px;
    }

    .hero-subtitle {
      font-size: 16px;
      margin-bottom: 25px;
    }

    .section-title {
      font-size: 24px;
      margin-bottom: 30px;
    }

    .article-title {
      font-size: 26px;
      line-height: 1.3;
    }

    .page-header h1 {
      font-size: 26px;
    }

    .page-header {
      padding: 40px 20px;
    }

    /* --- LAYOUTS --- */
    .program-card-container, .news-card-container, .profile-card-container, .footer-container, .cta-buttons-container {
      flex-direction: column;
      gap: 20px;
    }

    .program-detail-item, .program-detail-item.reverse {
      flex-direction: column;
      gap: 30px;
      margin-bottom: 50px;
    }

    .news-list-container {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    /* --- FORMULIR --- */
    .form-container {
      padding: 30px 20px;
      margin: 0 10px;
    }

    /* --- CTA SECTION --- */
    .cta-section h2 {
      font-size: 24px;
    }

    .cta-section p {
      font-size: 16px;
    }

    .cta-main-button, .cta-secondary-button {
      padding: 12px 24px;
      font-size: 14px;
      width: 100%;
      text-align: center;
    }
  }

  /* Mobile Portrait */
  @media screen and (max-width: 480px) {
    .site-header {
      padding: 0 15px;
    }

    .header-container {
      padding: 10px 0;
      min-height: 60px;
    }

    .nav-links {
      gap: 8px;
      font-size: 12px;
      margin: 10px 0;
    }

    .nav-links a {
      padding: 6px 10px;
      font-size: 12px;
    }

    .logo a {
      font-size: 16px;
    }

    .header-cta {
      margin-top: 8px;
    }

    .cta-button {
      padding: 8px 16px;
      font-size: 12px;
    }

    .hero-section {
      padding: 40px 15px;
    }

    .hero-title {
      font-size: 24px;
    }

    .hero-subtitle {
      font-size: 14px;
    }

    .hero-button {
      padding: 12px 24px;
      font-size: 16px;
    }

    .programs-section, .news-section, .about-section, .program-detail-section, .news-list-section, .form-section, .article-detail-section, .cta-section, .site-footer {
      padding: 40px 15px;
    }

    .section-title {
      font-size: 20px;
      margin-bottom: 25px;
    }

    .program-card {
      padding: 25px 20px;
    }

    .program-card h3 {
      font-size: 18px;
    }

    .program-card p {
      font-size: 14px;
    }

    .news-card-container {
      gap: 15px;
    }

    .form-container {
      padding: 20px 15px;
      margin: 0 5px;
    }

    .form-container h2 {
      font-size: 22px;
    }

    .form-group input, .form-group textarea {
      padding: 10px;
      font-size: 14px;
    }

    .form-group button {
      padding: 12px;
      font-size: 14px;
    }

    .page-header {
      padding: 30px 15px;
    }

    .page-header h1 {
      font-size: 22px;
    }

    .page-header p {
      font-size: 14px;
    }

    .article-title {
      font-size: 22px;
    }

    .article-content {
      font-size: 16px;
    }

    .cta-section h2 {
      font-size: 20px;
    }

    .cta-section p {
      font-size: 14px;
    }

    .footer-container {
      gap: 20px;
    }

    .footer-container h3 {
      font-size: 16px;
    }
  }

  /* =================================== */
  /* PERBAIKAN KHUSUS UNTUK MOBILE       */
  /* =================================== */

  /* Memastikan tidak ada horizontal scroll */
  @media screen and (max-width: 768px) {
    html, body {
      overflow-x: hidden;
      width: 100%;
      position: relative;
    }

    /* Memastikan semua container tidak overflow */
    .header-container,
    .hero-section,
    .programs-section,
    .news-section,
    .about-section,
    .program-detail-section,
    .news-list-section,
    .form-section,
    .article-detail-section,
    .cta-section,
    .site-footer {
      width: 100%;
      max-width: 100%;
      box-sizing: border-box;
      overflow-x: hidden;
    }

    /* Memastikan semua flex container responsive */
    .program-card-container,
    .news-card-container,
    .profile-card-container,
    .footer-container,
    .cta-buttons-container {
      width: 100%;
      max-width: 100%;
      box-sizing: border-box;
    }

    /* Memastikan grid container responsive */
    .news-list-container {
      width: 100%;
      max-width: 100%;
      box-sizing: border-box;
    }

    /* Memastikan form responsive */
    .form-container {
      width: 100%;
      max-width: 100%;
      box-sizing: border-box;
    }

    .form-group input,
    .form-group textarea {
      width: 100%;
      max-width: 100%;
      box-sizing: border-box;
      font-size: 16px !important; /* Memastikan font size minimum untuk mencegah zoom */
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }

    /* Memastikan gambar responsive */
    .news-image,
    .article-image,
    .program-detail-image img {
      width: 100%;
      max-width: 100%;
      height: auto;
      object-fit: cover;
    }

    /* Memastikan teks tidak overflow */
    .hero-title,
    .section-title,
    .article-title,
    .page-header h1 {
      word-wrap: break-word;
      overflow-wrap: break-word;
      hyphens: auto;
    }

    /* Memastikan button responsive */
    .hero-button,
    .cta-main-button,
    .cta-secondary-button,
    .form-group button {
      width: 100%;
      max-width: 100%;
      box-sizing: border-box;
      text-align: center;
    }
  }

  /* ========================================================== */
  /* FINAL OVERRIDE - PRIORITAS TERTINGGI UNTUK MOBILE         */
  /* ========================================================== */

  /* MOBILE DETECTION SUPER AGRESIF */
  @media screen and (max-width: 768px),
         screen and (max-device-width: 768px),
         only screen and (max-width: 768px),
         only screen and (max-device-width: 768px) {

    /* FORCE SEMUA CONTAINER MOBILE */
    .site-header,
    .header-container,
    .hero-section,
    .programs-section,
    .news-section,
    .about-section,
    .program-detail-section,
    .news-list-section,
    .form-section,
    .article-detail-section,
    .cta-section,
    .site-footer {
      width: 100% !important;
      max-width: 100% !important;
      box-sizing: border-box !important;
    }

    /* FORCE FLEX CONTAINERS MOBILE */
    .header-container {
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      text-align: center !important;
      padding: 10px 0 !important;
      height: auto !important;
    }

    .nav-links {
      display: flex !important;
      flex-direction: row !important;
      flex-wrap: wrap !important;
      justify-content: center !important;
      align-items: center !important;
      gap: 8px !important;
      margin: 10px 0 !important;
      width: 100% !important;
    }

    .program-card-container {
      display: flex !important;
      flex-direction: column !important;
      align-items: stretch !important;
      gap: 15px !important;
      width: 100% !important;
    }

    .news-card-container {
      display: flex !important;
      flex-direction: column !important;
      align-items: stretch !important;
      gap: 15px !important;
      width: 100% !important;
    }

    .cta-buttons-container {
      display: flex !important;
      flex-direction: column !important;
      align-items: stretch !important;
      gap: 10px !important;
      width: 100% !important;
    }

    .footer-container {
      display: flex !important;
      flex-direction: column !important;
      gap: 20px !important;
      width: 100% !important;
    }

    /* FORCE GRID CONTAINERS MOBILE */
    .news-list-container {
      display: grid !important;
      grid-template-columns: 1fr !important;
      gap: 15px !important;
      width: 100% !important;
    }

    /* FORCE INDIVIDUAL ELEMENTS MOBILE */
    .program-card,
    .news-card {
      width: 100% !important;
      max-width: 100% !important;
      flex: none !important;
      margin: 0 !important;
    }

    .cta-main-button,
    .cta-secondary-button {
      width: 100% !important;
      max-width: 100% !important;
      display: block !important;
      text-align: center !important;
      flex: none !important;
    }

    /* FORCE TYPOGRAPHY MOBILE */
    .hero-title {
      font-size: 28px !important;
      line-height: 1.3 !important;
      text-align: center !important;
    }

    .section-title {
      font-size: 24px !important;
      text-align: center !important;
    }

    .hero-subtitle {
      font-size: 18px !important;
      line-height: 1.4 !important;
    }

    .program-card h3,
    .news-title {
      font-size: 18px !important;
    }

    .program-card p,
    .news-excerpt {
      font-size: 16px !important;
      line-height: 1.5 !important;
    }

    .cta-section h2 {
      font-size: 26px !important;
    }

    .cta-section p {
      font-size: 18px !important;
    }

    .nav-links a {
      font-size: 16px !important;
      padding: 8px 12px !important;
      white-space: nowrap !important;
    }

    /* FORCE SPACING MOBILE */
    .hero-section {
      padding: 30px 15px !important;
    }

    .programs-section,
    .news-section,
    .cta-section {
      padding: 30px 15px !important;
    }

    .site-header {
      padding: 0 10px !important;
    }

    /* PREVENT HORIZONTAL SCROLL */
    html, body {
      overflow-x: hidden !important;
      width: 100vw !important;
      max-width: 100vw !important;
    }

    * {
      max-width: 100% !important;
      box-sizing: border-box !important;
    }
  }

  /* FORCE MOBILE PADA USER AGENT MOBILE */
  @media only screen and (max-device-width: 768px) {
    .program-card-container,
    .news-card-container,
    .cta-buttons-container,
    .footer-container {
      flex-direction: column !important;
    }

    .header-container {
      flex-direction: column !important;
      text-align: center !important;
    }

    .nav-links {
      flex-wrap: wrap !important;
      justify-content: center !important;
    }
  }

</style>


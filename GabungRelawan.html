<!DOCTYPE html>
<html lang="id">
<head>
    <base target="_top">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Gabung Relawan - Relawan TIK Kaltim</title>
    <meta name="description" content="Bergabunglah menjadi relawan TIK Kalimantan Timur - Formulir Pendaftaran">
    <?!= include('Stylesheet'); ?>
</head>
<body>
    <!-- HEADER -->
    <header class="site-header">
      <div class="header-container">
        <div class="logo">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Relawan TIK Kaltim</a>
        </div>
        <nav class="nav-links">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a>
        </nav>
        <div class="header-cta">
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=gabung_relawan" class="cta-button">Gabung Relawan!</a>
        </div>
      </div>
    </header>

    <main>
        <section class="page-header">
            <h1>Gabung Menjadi Relawan</h1>
            <p>Jadilah bagian dari gerakan literasi digital di Kalimantan Timur.</p>
        </section>
        <section class="form-section">
            <div class="form-container">
                <h2>Formulir Pendaftaran</h2>
                <p>Isi data diri Anda dengan lengkap dan benar. Kami akan segera menghubungi Anda.</p>
                <form id="volunteer-form">
                    <div class="form-group">
                        <label for="nama">Nama Lengkap</label>
                        <input type="text" id="nama" name="nama" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Alamat Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="no_wa">Nomor WhatsApp Aktif</label>
                        <input type="tel" id="no_wa" name="no_wa" required>
                    </div>
                    <div class="form-group">
                        <label for="domisili">Domisili (Kabupaten/Kota)</label>
                        <input type="text" id="domisili" name="domisili" required>
                    </div>
                    <div class="form-group">
                        <label for="alasan">Alasan Bergabung</label>
                        <textarea id="alasan" name="alasan" rows="5" required></textarea>
                    </div>
                    <div class="form-group">
                        <button type="submit" id="submit-button">Kirim Pendaftaran</button>
                    </div>
                </form>
                <div id="status-message"></div>
            </div>
        </section>
    </main>

    <!-- FOOTER -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-about"><h3>Relawan TIK Kaltim</h3><p>Organisasi sosial nirlaba yang mendedikasikan diri untuk akselerasi dan penetrasi literasi digital di Kalimantan Timur.</p></div>
            <div class="footer-links"><h3>Navigasi</h3><ul><li><a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a></li></ul></div>
            <div class="footer-contact"><h3>Hubungi Kami</h3><p>Email: <EMAIL></p><p>Sekretariat: [Alamat Sekretariat]</p></div>
            <div class="footer-social"><h3>Ikuti Kami</h3><div class="social-icons"><a href="#">[FB]</a> <a href="#">[IG]</a> <a href="#">[TW]</a> <a href="#">[YT]</a></div></div>
        </div>
        <div class="footer-bottom"><p>© 2025 Relawan TIK Provinsi Kalimantan Timur. Hak Cipta Dilindungi.</p></div>
    </footer>

    <script>
      const form = document.getElementById('volunteer-form');
      const submitButton = document.getElementById('submit-button');
      const statusMessage = document.getElementById('status-message');

      form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        submitButton.disabled = true;
        submitButton.textContent = 'Mengirim...';
        statusMessage.textContent = '';
        statusMessage.className = '';

        const formData = {
          nama: form.nama.value,
          email: form.email.value,
          no_wa: form.no_wa.value,
          domisili: form.domisili.value,
          alasan: form.alasan.value
        };

        google.script.run
          .withSuccessHandler(function(response) {
            if (response.status === "success") {
              statusMessage.textContent = response.message;
              statusMessage.classList.add('success');
              form.reset();
            } else {
              statusMessage.textContent = 'Gagal: ' + response.message;
              statusMessage.classList.add('error');
            }
            submitButton.disabled = false;
            submitButton.textContent = 'Kirim Pendaftaran';
          })
          .withFailureHandler(function(error) {
            statusMessage.textContent = 'Gagal: Terjadi kesalahan: ' + error.message;
            statusMessage.classList.add('error');
            submitButton.disabled = false;
            submitButton.textContent = 'Kirim Pendaftaran';
          })
          .saveVolunteerData(formData);
      });

      // Script untuk memastikan responsive bekerja di Google Apps Script
      (function() {
        // Memastikan viewport meta tag bekerja
        function setViewport() {
          var viewport = document.querySelector('meta[name="viewport"]');
          if (viewport) {
            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
          }
        }

        // Mencegah zoom pada input focus di iOS
        function preventZoom() {
          var inputs = document.querySelectorAll('input, textarea, select');
          inputs.forEach(function(input) {
            input.addEventListener('focus', function() {
              if (window.innerWidth < 768) {
                var fontSize = window.getComputedStyle(input).fontSize;
                if (parseInt(fontSize) < 16) {
                  input.style.fontSize = '16px';
                }
              }
            });
          });
        }

        // Memastikan tidak ada horizontal scroll
        function preventHorizontalScroll() {
          document.body.style.overflowX = 'hidden';
          document.documentElement.style.overflowX = 'hidden';
        }

        // Jalankan saat DOM ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', function() {
            setViewport();
            preventZoom();
            preventHorizontalScroll();
          });
        } else {
          setViewport();
          preventZoom();
          preventHorizontalScroll();
        }

        // Jalankan saat window resize
        window.addEventListener('resize', function() {
          preventHorizontalScroll();
        });
      })();
    </script>

</body>
</html>


<!DOCTYPE html>
<html lang="id">
<head>
    <base target="_top">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title>Gabung Relawan - Relawan TIK Kaltim</title>
    <meta name="description" content="Bergabunglah menjadi relawan TIK Kalimantan Timur - Formulir Pendaftaran">

    <!-- CSS Inline untuk memaksa responsivitas -->
    <style>
      /* Force responsive behavior */
      html {
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
        -webkit-text-size-adjust: 100% !important;
        -ms-text-size-adjust: 100% !important;
      }

      body {
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        box-sizing: border-box !important;
      }

      * {
        box-sizing: border-box !important;
        max-width: 100% !important;
      }

      /* Force mobile layout on small screens */
      @media screen and (max-width: 768px) {
        html, body {
          width: 100vw !important;
          max-width: 100vw !important;
          overflow-x: hidden !important;
        }

        .site-header {
          width: 100% !important;
          padding: 0 15px !important;
        }

        .header-container {
          flex-direction: column !important;
          width: 100% !important;
          text-align: center !important;
          padding: 10px 0 !important;
        }

        .nav-links {
          flex-wrap: wrap !important;
          justify-content: center !important;
          gap: 10px !important;
          margin: 10px 0 !important;
        }

        .nav-links a {
          font-size: 14px !important;
          padding: 8px 12px !important;
        }

        .form-section {
          padding: 40px 15px !important;
        }

        .form-container {
          width: 100% !important;
          max-width: 100% !important;
          margin: 0 !important;
          padding: 20px 15px !important;
        }

        .form-group input,
        .form-group textarea {
          width: 100% !important;
          font-size: 16px !important;
          padding: 12px !important;
        }

        .form-group button {
          width: 100% !important;
          padding: 15px !important;
          font-size: 16px !important;
        }
      }
    </style>

    <?!= include('Stylesheet'); ?>
</head>
<body>
    <!-- HEADER -->
    <header class="site-header">
      <div class="header-container">
        <div class="logo">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Relawan TIK Kaltim</a>
        </div>
        <nav class="nav-links">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a>
        </nav>
        <div class="header-cta">
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=gabung_relawan" class="cta-button">Gabung Relawan!</a>
        </div>
      </div>
    </header>

    <main>
        <section class="page-header">
            <h1>Gabung Menjadi Relawan</h1>
            <p>Jadilah bagian dari gerakan literasi digital di Kalimantan Timur.</p>
        </section>
        <section class="form-section">
            <div class="form-container">
                <h2>Formulir Pendaftaran</h2>
                <p>Isi data diri Anda dengan lengkap dan benar. Kami akan segera menghubungi Anda.</p>
                <form id="volunteer-form">
                    <div class="form-group">
                        <label for="nama">Nama Lengkap</label>
                        <input type="text" id="nama" name="nama" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Alamat Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="no_wa">Nomor WhatsApp Aktif</label>
                        <input type="tel" id="no_wa" name="no_wa" required>
                    </div>
                    <div class="form-group">
                        <label for="domisili">Domisili (Kabupaten/Kota)</label>
                        <input type="text" id="domisili" name="domisili" required>
                    </div>
                    <div class="form-group">
                        <label for="alasan">Alasan Bergabung</label>
                        <textarea id="alasan" name="alasan" rows="5" required></textarea>
                    </div>
                    <div class="form-group">
                        <button type="submit" id="submit-button">Kirim Pendaftaran</button>
                    </div>
                </form>
                <div id="status-message"></div>
            </div>
        </section>
    </main>

    <!-- FOOTER -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-about"><h3>Relawan TIK Kaltim</h3><p>Organisasi sosial nirlaba yang mendedikasikan diri untuk akselerasi dan penetrasi literasi digital di Kalimantan Timur.</p></div>
            <div class="footer-links"><h3>Navigasi</h3><ul><li><a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a></li></ul></div>
            <div class="footer-contact"><h3>Hubungi Kami</h3><p>Email: <EMAIL></p><p>Sekretariat: [Alamat Sekretariat]</p></div>
            <div class="footer-social"><h3>Ikuti Kami</h3><div class="social-icons"><a href="#">[FB]</a> <a href="#">[IG]</a> <a href="#">[TW]</a> <a href="#">[YT]</a></div></div>
        </div>
        <div class="footer-bottom"><p>© 2025 Relawan TIK Provinsi Kalimantan Timur. Hak Cipta Dilindungi.</p></div>
    </footer>

    <script>
      const form = document.getElementById('volunteer-form');
      const submitButton = document.getElementById('submit-button');
      const statusMessage = document.getElementById('status-message');

      form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        submitButton.disabled = true;
        submitButton.textContent = 'Mengirim...';
        statusMessage.textContent = '';
        statusMessage.className = '';

        const formData = {
          nama: form.nama.value,
          email: form.email.value,
          no_wa: form.no_wa.value,
          domisili: form.domisili.value,
          alasan: form.alasan.value
        };

        google.script.run
          .withSuccessHandler(function(response) {
            if (response.status === "success") {
              statusMessage.textContent = response.message;
              statusMessage.classList.add('success');
              form.reset();
            } else {
              statusMessage.textContent = 'Gagal: ' + response.message;
              statusMessage.classList.add('error');
            }
            submitButton.disabled = false;
            submitButton.textContent = 'Kirim Pendaftaran';
          })
          .withFailureHandler(function(error) {
            statusMessage.textContent = 'Gagal: Terjadi kesalahan: ' + error.message;
            statusMessage.classList.add('error');
            submitButton.disabled = false;
            submitButton.textContent = 'Kirim Pendaftaran';
          })
          .saveVolunteerData(formData);
      });

      // Script AGRESIF untuk memaksa responsive di Google Apps Script
      (function() {
        // Deteksi apakah di dalam iframe
        function isInIframe() {
          try {
            return window.self !== window.top;
          } catch (e) {
            return true;
          }
        }

        // Force viewport meta tag
        function forceViewport() {
          // Hapus viewport yang ada
          var existingViewport = document.querySelector('meta[name="viewport"]');
          if (existingViewport) {
            existingViewport.remove();
          }

          // Buat viewport baru
          var viewport = document.createElement('meta');
          viewport.name = 'viewport';
          viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no';
          document.head.insertBefore(viewport, document.head.firstChild);

          // Jika di iframe, coba set viewport di parent
          if (isInIframe()) {
            try {
              var parentViewport = window.parent.document.querySelector('meta[name="viewport"]');
              if (!parentViewport) {
                parentViewport = window.parent.document.createElement('meta');
                parentViewport.name = 'viewport';
                parentViewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
                window.parent.document.head.appendChild(parentViewport);
              }
            } catch (e) {
              console.log('Cannot access parent frame');
            }
          }
        }

        // Force responsive CSS
        function forceResponsiveCSS() {
          var style = document.createElement('style');
          style.textContent = `
            html, body {
              width: 100vw !important;
              max-width: 100vw !important;
              overflow-x: hidden !important;
              box-sizing: border-box !important;
            }

            * {
              box-sizing: border-box !important;
            }

            @media screen and (max-width: 768px) {
              .site-header {
                padding: 0 15px !important;
              }

              .header-container {
                flex-direction: column !important;
                text-align: center !important;
                padding: 10px 0 !important;
              }

              .nav-links {
                flex-wrap: wrap !important;
                justify-content: center !important;
                gap: 10px !important;
                margin: 10px 0 !important;
              }

              .form-section {
                padding: 40px 15px !important;
              }

              .form-container {
                width: calc(100% - 30px) !important;
                max-width: calc(100% - 30px) !important;
                margin: 0 15px !important;
                padding: 20px 15px !important;
              }

              .form-group input,
              .form-group textarea {
                width: 100% !important;
                font-size: 16px !important;
              }
            }
          `;
          document.head.appendChild(style);
        }

        // Force mobile layout detection
        function forceMobileLayout() {
          var isMobile = window.innerWidth <= 768 ||
                        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

          if (isMobile) {
            document.body.classList.add('mobile-device');

            // Force mobile styles
            var elements = document.querySelectorAll('.header-container, .nav-links, .form-container');
            elements.forEach(function(el) {
              if (window.innerWidth <= 768) {
                el.style.cssText += `
                  width: 100% !important;
                  max-width: 100% !important;
                  box-sizing: border-box !important;
                `;
              }
            });
          }
        }

        // Prevent zoom on input focus
        function preventZoom() {
          var inputs = document.querySelectorAll('input, textarea, select');
          inputs.forEach(function(input) {
            input.style.fontSize = '16px';
            input.addEventListener('focus', function() {
              this.style.fontSize = '16px';
            });
          });
        }

        // Force no horizontal scroll
        function preventHorizontalScroll() {
          document.documentElement.style.overflowX = 'hidden';
          document.body.style.overflowX = 'hidden';

          // Jika di iframe, coba set di parent juga
          if (isInIframe()) {
            try {
              window.parent.document.documentElement.style.overflowX = 'hidden';
              window.parent.document.body.style.overflowX = 'hidden';
            } catch (e) {
              console.log('Cannot access parent frame');
            }
          }
        }

        // Initialize semua fungsi
        function init() {
          forceViewport();
          forceResponsiveCSS();
          forceMobileLayout();
          preventZoom();
          preventHorizontalScroll();
        }

        // Jalankan saat DOM ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', init);
        } else {
          init();
        }

        // Jalankan ulang saat resize
        window.addEventListener('resize', function() {
          forceMobileLayout();
          preventHorizontalScroll();
        });

        // Jalankan ulang setelah delay (untuk memastikan)
        setTimeout(init, 100);
        setTimeout(init, 500);
        setTimeout(init, 1000);
      })();
    </script>

</body>
</html>


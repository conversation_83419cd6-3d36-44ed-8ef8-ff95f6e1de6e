<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Debug Mobile Layout</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1e3a8a;
            color: white;
        }
        
        .debug-box {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-weight: bold;
        }
        
        .success { background: #16a34a; }
        .warning { background: #f59e0b; }
        .error { background: #dc2626; }
        
        pre {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #1d4ed8;
        }
    </style>
</head>
<body>
    <h1>🔍 Debug Mobile Layout</h1>
    <p>Tool untuk debug responsivitas website</p>
    
    <div class="debug-box">
        <h3>📱 Device Information</h3>
        <div id="device-info"></div>
    </div>
    
    <div class="debug-box">
        <h3>🎯 Mobile Detection</h3>
        <div id="mobile-detection"></div>
    </div>
    
    <div class="debug-box">
        <h3>📐 CSS Media Query Test</h3>
        <div id="media-query-test"></div>
    </div>
    
    <div class="debug-box">
        <h3>🔧 Actions</h3>
        <button onclick="testIndex()">Test Index.html</button>
        <button onclick="forceRefresh()">Force Refresh</button>
        <button onclick="clearCache()">Clear Cache</button>
    </div>
    
    <div class="debug-box">
        <h3>📋 Test Results</h3>
        <div id="test-results"></div>
    </div>

    <script>
        function updateInfo() {
            // Device Info
            const deviceInfo = document.getElementById('device-info');
            deviceInfo.innerHTML = `
                <div><strong>Screen:</strong> ${window.screen.width} x ${window.screen.height}</div>
                <div><strong>Window:</strong> ${window.innerWidth} x ${window.innerHeight}</div>
                <div><strong>Device Pixel Ratio:</strong> ${window.devicePixelRatio}</div>
                <div><strong>User Agent:</strong> ${navigator.userAgent}</div>
                <div><strong>Touch Support:</strong> ${'ontouchstart' in window ? 'Yes' : 'No'}</div>
                <div><strong>Orientation:</strong> ${window.orientation !== undefined ? window.orientation + '°' : 'Unknown'}</div>
            `;
            
            // Mobile Detection
            const isMobileWidth = window.innerWidth <= 768;
            const isMobileScreen = window.screen.width <= 768;
            const isMobileUA = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            const mobileDetection = document.getElementById('mobile-detection');
            mobileDetection.innerHTML = `
                <div class="status ${isMobileWidth ? 'success' : 'error'}">
                    Window Width ≤ 768px: ${isMobileWidth ? 'YES' : 'NO'} (${window.innerWidth}px)
                </div>
                <div class="status ${isMobileScreen ? 'success' : 'error'}">
                    Screen Width ≤ 768px: ${isMobileScreen ? 'YES' : 'NO'} (${window.screen.width}px)
                </div>
                <div class="status ${isMobileUA ? 'success' : 'warning'}">
                    Mobile User Agent: ${isMobileUA ? 'YES' : 'NO'}
                </div>
            `;
            
            // Media Query Test
            const mediaQueryTest = document.getElementById('media-query-test');
            const mq768 = window.matchMedia('(max-width: 768px)').matches;
            const mq480 = window.matchMedia('(max-width: 480px)').matches;
            
            mediaQueryTest.innerHTML = `
                <div class="status ${mq768 ? 'success' : 'error'}">
                    @media (max-width: 768px): ${mq768 ? 'ACTIVE' : 'INACTIVE'}
                </div>
                <div class="status ${mq480 ? 'success' : 'warning'}">
                    @media (max-width: 480px): ${mq480 ? 'ACTIVE' : 'INACTIVE'}
                </div>
            `;
        }
        
        function testIndex() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<div class="status warning">Testing Index.html...</div>';
            
            // Simulate opening Index.html
            setTimeout(() => {
                const shouldBeMobile = window.innerWidth <= 768 || 
                                     window.screen.width <= 768 ||
                                     /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                
                results.innerHTML = `
                    <div class="status ${shouldBeMobile ? 'success' : 'error'}">
                        Expected Mobile Layout: ${shouldBeMobile ? 'YES' : 'NO'}
                    </div>
                    <div class="status warning">
                        Recommendation: ${shouldBeMobile ? 'Index.html should display mobile layout' : 'Index.html should display desktop layout'}
                    </div>
                    <div style="margin-top: 10px;">
                        <a href="Index.html" style="color: #fbbf24; text-decoration: underline;">
                            → Open Index.html to test
                        </a>
                    </div>
                `;
            }, 1000);
        }
        
        function forceRefresh() {
            location.reload(true);
        }
        
        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(function(names) {
                    names.forEach(function(name) {
                        caches.delete(name);
                    });
                });
            }
            localStorage.clear();
            sessionStorage.clear();
            alert('Cache cleared! Please refresh the page.');
        }
        
        // Update info on load and resize
        updateInfo();
        window.addEventListener('resize', updateInfo);
        window.addEventListener('orientationchange', function() {
            setTimeout(updateInfo, 100);
        });
        
        // Auto-update every 2 seconds
        setInterval(updateInfo, 2000);
    </script>
</body>
</html>

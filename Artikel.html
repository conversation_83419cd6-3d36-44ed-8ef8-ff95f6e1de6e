<!DOCTYPE html>
<html lang="id">
  <head>
    <base target="_top">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Artikel - Relawan TIK Kaltim</title>
    <meta name="description" content="Artikel dari Relawan TIK Kalimantan Timur">
    <?!= include('Stylesheet'); ?>
  </head>
  <body>
    <!-- HEADER -->
    <header class="site-header">
      <div class="header-container">
        <div class="logo">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Relawan TIK Kaltim</a>
        </div>
        <nav class="nav-links">
          <a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang <PERSON></a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a>
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a>
        </nav>
        <div class="header-cta">
          <a href="<?= ScriptApp.getService().getUrl(); ?>?page=gabung_relawan" class="cta-button">Gabung Relawan!</a>
        </div>
      </div>
    </header>

    <main>
      <section class="article-detail-section">
        <div class="article-container">
          <? if (typeof article !== 'undefined' && article) { ?>
            <div class="article-header">
              <p class="article-meta">Kategori: <?= article.kategori ?></p>
              <h1 class="article-title"><?= article.judul ?></h1>
            </div>
            <img class="article-image" src="<?= article.gambar_url ?>" alt="Gambar untuk artikel: <?= article.judul ?>">
            <div class="article-content">
              <?!= article.isi_artikel ?>
            </div>
          <? } else { ?>
            <h1>Artikel Tidak Ditemukan</h1>
            <p>Artikel yang Anda cari tidak ada atau telah dihapus.</p>
            <a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Kembali ke Daftar Berita</a>
          <? } ?>
        </div>
      </section>
    </main>
    
    <!-- FOOTER -->
    <footer class="site-footer">
        <div class="footer-container">
            <div class="footer-about"><h3>Relawan TIK Kaltim</h3><p>Organisasi sosial nirlaba yang mendedikasikan diri untuk akselerasi dan penetrasi literasi digital di Kalimantan Timur.</p></div>
            <div class="footer-links"><h3>Navigasi</h3><ul><li><a href="<?= ScriptApp.getService().getUrl(); ?>">Beranda</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=tentang_kami">Tentang Kami</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=program">Program</a></li><li><a href="<?= ScriptApp.getService().getUrl(); ?>?page=berita">Berita</a></li></ul></div>
            <div class="footer-contact"><h3>Hubungi Kami</h3><p>Email: <EMAIL></p><p>Sekretariat: [Alamat Sekretariat]</p></div>
            <div class="footer-social"><h3>Ikuti Kami</h3><div class="social-icons"><a href="#">[FB]</a> <a href="#">[IG]</a> <a href="#">[TW]</a> <a href="#">[YT]</a></div></div>
        </div>
        <div class="footer-bottom"><p>© 2025 Relawan TIK Provinsi Kalimantan Timur. Hak Cipta Dilindungi.</p></div>
    </footer>

    <script>
      // Script untuk memastikan responsive bekerja di Google Apps Script
      (function() {
        // Memastikan viewport meta tag bekerja
        function setViewport() {
          var viewport = document.querySelector('meta[name="viewport"]');
          if (viewport) {
            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
          }
        }

        // Memastikan tidak ada horizontal scroll
        function preventHorizontalScroll() {
          document.body.style.overflowX = 'hidden';
          document.documentElement.style.overflowX = 'hidden';
        }

        // Jalankan saat DOM ready
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', function() {
            setViewport();
            preventHorizontalScroll();
          });
        } else {
          setViewport();
          preventHorizontalScroll();
        }

        // Jalankan saat window resize
        window.addEventListener('resize', function() {
          preventHorizontalScroll();
        });
      })();
    </script>

  </body>
</html>

